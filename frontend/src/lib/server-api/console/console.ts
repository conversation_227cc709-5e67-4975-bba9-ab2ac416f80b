/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { AgenciesResponseBody, ConsoleUsersResponseBody } from '../shop-api.schemas';

/**
 * @summary Get Agencies
 */
export const getAgencies = () => {
  return customServerInstance<AgenciesResponseBody>({ url: `/console/agencies`, method: 'GET' });
};
/**
 * @summary Get Console Users
 */
export const getConsoleUsers = () => {
  return customServerInstance<ConsoleUsersResponseBody>({ url: `/console/users`, method: 'GET' });
};
export type GetAgenciesResult = NonNullable<Awaited<ReturnType<typeof getAgencies>>>;
export type GetConsoleUsersResult = NonNullable<Awaited<ReturnType<typeof getConsoleUsers>>>;
