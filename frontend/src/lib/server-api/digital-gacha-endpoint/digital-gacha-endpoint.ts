/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type {
  CompleteBadgeResponseBody,
  DigitalGachaPullResponseBody,
  DigitalGachaPullableCountResponseBody,
  PullDigitalGachaRequest,
} from '../shop-api.schemas';

/**
 * @summary Pull
 */
export const pull = (pullDigitalGachaRequest: PullDigitalGachaRequest) => {
  return customServerInstance<DigitalGachaPullResponseBody>({
    url: `/shops/digital-gacha/pull`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: pullDigitalGachaRequest,
  });
};
/**
 * @summary Get Complete Badge
 */
export const getCompleteBadge = (itemId: number) => {
  return customServerInstance<CompleteBadgeResponseBody>({
    url: `/shops/digital-gacha/${itemId}/complete-badge`,
    method: 'GET',
  });
};
/**
 * @summary Get Pullable Gacha Count
 */
export const getPullableGachaCount = (itemId: number) => {
  return customServerInstance<DigitalGachaPullableCountResponseBody>({
    url: `/shops/digital-gacha/${itemId}/pullable-count`,
    method: 'GET',
  });
};
export type PullResult = NonNullable<Awaited<ReturnType<typeof pull>>>;
export type GetCompleteBadgeResult = NonNullable<Awaited<ReturnType<typeof getCompleteBadge>>>;
export type GetPullableGachaCountResult = NonNullable<Awaited<ReturnType<typeof getPullableGachaCount>>>;
