/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type {
  CreateSingleOrderRequest,
  FinalizeCreditCard3DSecureRequest,
  SingleOrderResponseBody,
} from '../client-shop-api.schemas';
import type { Key } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Create Order
 */
export const createOrder = (createSingleOrderRequest: CreateSingleOrderRequest) => {
  return customClientInstance<SingleOrderResponseBody>({
    url: `/single-order`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createSingleOrderRequest,
  });
};

export const getCreateOrderMutationFetcher = () => {
  return (_: Key, { arg }: { arg: CreateSingleOrderRequest }): Promise<SingleOrderResponseBody> => {
    return createOrder(arg);
  };
};
export const getCreateOrderMutationKey = () => [`/single-order`] as const;

export type CreateOrderMutationResult = NonNullable<Awaited<ReturnType<typeof createOrder>>>;
export type CreateOrderMutationError = void;

/**
 * @summary Create Order
 */
export const useCreateOrder = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof createOrder>>,
    TError,
    Key,
    CreateSingleOrderRequest,
    Awaited<ReturnType<typeof createOrder>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateOrderMutationKey();
  const swrFn = getCreateOrderMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Finalize Credit Card 3 D Secure Single Order
 */
export const finalizeCreditCard3DSecureSingleOrder = (
  finalizeCreditCard3DSecureRequest: FinalizeCreditCard3DSecureRequest,
) => {
  return customClientInstance<void>({
    url: `/single-order/finalize-credit-card-3d-secure`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: finalizeCreditCard3DSecureRequest,
  });
};

export const getFinalizeCreditCard3DSecureSingleOrderMutationFetcher = () => {
  return (_: Key, { arg }: { arg: FinalizeCreditCard3DSecureRequest }): Promise<void> => {
    return finalizeCreditCard3DSecureSingleOrder(arg);
  };
};
export const getFinalizeCreditCard3DSecureSingleOrderMutationKey = () =>
  [`/single-order/finalize-credit-card-3d-secure`] as const;

export type FinalizeCreditCard3DSecureSingleOrderMutationResult = NonNullable<
  Awaited<ReturnType<typeof finalizeCreditCard3DSecureSingleOrder>>
>;
export type FinalizeCreditCard3DSecureSingleOrderMutationError = void;

/**
 * @summary Finalize Credit Card 3 D Secure Single Order
 */
export const useFinalizeCreditCard3DSecureSingleOrder = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof finalizeCreditCard3DSecureSingleOrder>>,
    TError,
    Key,
    FinalizeCreditCard3DSecureRequest,
    Awaited<ReturnType<typeof finalizeCreditCard3DSecureSingleOrder>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getFinalizeCreditCard3DSecureSingleOrderMutationKey();
  const swrFn = getFinalizeCreditCard3DSecureSingleOrderMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
