/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { AgenciesResponseBody, ConsoleUsersResponseBody } from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';

/**
 * @summary Get Agencies
 */
export const getAgencies = () => {
  return customClientInstance<AgenciesResponseBody>({ url: `/console/agencies`, method: 'GET' });
};

export const getGetAgenciesKey = () => [`/console/agencies`] as const;

export type GetAgenciesQueryResult = NonNullable<Awaited<ReturnType<typeof getAgencies>>>;
export type GetAgenciesQueryError = void;

/**
 * @summary Get Agencies
 */
export const useGetAgencies = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getAgencies>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAgenciesKey() : null));
  const swrFn = () => getAgencies();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Console Users
 */
export const getConsoleUsers = () => {
  return customClientInstance<ConsoleUsersResponseBody>({ url: `/console/users`, method: 'GET' });
};

export const getGetConsoleUsersKey = () => [`/console/users`] as const;

export type GetConsoleUsersQueryResult = NonNullable<Awaited<ReturnType<typeof getConsoleUsers>>>;
export type GetConsoleUsersQueryError = void;

/**
 * @summary Get Console Users
 */
export const useGetConsoleUsers = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getConsoleUsers>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetConsoleUsersKey() : null));
  const swrFn = () => getConsoleUsers();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
