/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type {
  CompleteBadgeResponseBody,
  DigitalGachaPullResponseBody,
  DigitalGachaPullableCountResponseBody,
  PullDigitalGachaRequest,
} from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Pull
 */
export const pull = (pullDigitalGachaRequest: PullDigitalGachaRequest) => {
  return customClientInstance<DigitalGachaPullResponseBody>({
    url: `/shops/digital-gacha/pull`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: pullDigitalGachaRequest,
  });
};

export const getPullMutationFetcher = () => {
  return (_: Key, { arg }: { arg: PullDigitalGachaRequest }): Promise<DigitalGachaPullResponseBody> => {
    return pull(arg);
  };
};
export const getPullMutationKey = () => [`/shops/digital-gacha/pull`] as const;

export type PullMutationResult = NonNullable<Awaited<ReturnType<typeof pull>>>;
export type PullMutationError = void;

/**
 * @summary Pull
 */
export const usePull = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof pull>>,
    TError,
    Key,
    PullDigitalGachaRequest,
    Awaited<ReturnType<typeof pull>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getPullMutationKey();
  const swrFn = getPullMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Complete Badge
 */
export const getCompleteBadge = (itemId: number) => {
  return customClientInstance<CompleteBadgeResponseBody>({
    url: `/shops/digital-gacha/${itemId}/complete-badge`,
    method: 'GET',
  });
};

export const getGetCompleteBadgeKey = (itemId: number) => [`/shops/digital-gacha/${itemId}/complete-badge`] as const;

export type GetCompleteBadgeQueryResult = NonNullable<Awaited<ReturnType<typeof getCompleteBadge>>>;
export type GetCompleteBadgeQueryError = void;

/**
 * @summary Get Complete Badge
 */
export const useGetCompleteBadge = <TError = void>(
  itemId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getCompleteBadge>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!itemId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCompleteBadgeKey(itemId) : null));
  const swrFn = () => getCompleteBadge(itemId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Pullable Gacha Count
 */
export const getPullableGachaCount = (itemId: number) => {
  return customClientInstance<DigitalGachaPullableCountResponseBody>({
    url: `/shops/digital-gacha/${itemId}/pullable-count`,
    method: 'GET',
  });
};

export const getGetPullableGachaCountKey = (itemId: number) =>
  [`/shops/digital-gacha/${itemId}/pullable-count`] as const;

export type GetPullableGachaCountQueryResult = NonNullable<Awaited<ReturnType<typeof getPullableGachaCount>>>;
export type GetPullableGachaCountQueryError = void;

/**
 * @summary Get Pullable Gacha Count
 */
export const useGetPullableGachaCount = <TError = void>(
  itemId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getPullableGachaCount>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!itemId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetPullableGachaCountKey(itemId) : null));
  const swrFn = () => getPullableGachaCount(itemId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
