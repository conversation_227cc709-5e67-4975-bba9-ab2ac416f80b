'use server';

import { promises as fs } from 'fs';
import path from 'node:path';
import sharp from 'sharp';

// 統合されたインターフェース
interface BlurThumbOpt {
  url: string; // 画像URL
  imgSize: {
    // 出力サイズ
    width: number;
    height: number;
  };
  watermark?: string; // オプション: 透かし画像の絶対パス
  quality?: number;
}

/**
 * 画像をぼかし効果を追加する関数
 * 1. まず20x20にリサイズしてから指定サイズに拡大することで、ぼかし効果を作る
 * 2. オプションで透かしを追加
 * 3. JPEGで出力
 */
export async function buildBlurThumb({ url, imgSize, watermark, quality = 70 }: BlurThumbOpt) {
  try {
    // 1. 入力画像を読み込む
    let imageBuffer;

    // URLまたはローカルファイルパスから読み込み
    if (url.startsWith('http')) {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }
      imageBuffer = Buffer.from(await response.arrayBuffer());
    } else {
      // ローカルファイルからの読み込み - パストラバーサル対策
      // 安全な基本ディレクトリを定義
      const safeBasePath = path.resolve(process.cwd(), 'public');

      // パスの正規化と検証
      const normalizedPath = path.normalize(url);
      const absolutePath = path.isAbsolute(url) ? normalizedPath : path.join(safeBasePath, normalizedPath);

      // パストラバーサル攻撃の検出 (../などの存在チェック)
      if (!absolutePath.startsWith(safeBasePath)) {
        throw new Error('Security error: Attempted path traversal detected');
      }

      const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
      const fileExt = path.extname(absolutePath).toLowerCase();
      if (!allowedExtensions.includes(fileExt)) {
        throw new Error(`Security error: File type not allowed. Only ${allowedExtensions.join(', ')} are permitted`);
      }

      // 安全と判断されたパスからファイルを読み込み
      imageBuffer = await fs.readFile(absolutePath);
    }

    // 2. 20x20にダウンサイズしてから指定サイズにアップサイズ (ぼかし効果を作成)
    const smallImageBuffer = await sharp(imageBuffer)
      .resize(20, 20, {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 },
      })
      .toBuffer();

    let processedImage = sharp(smallImageBuffer).resize(imgSize.width, imgSize.height, {
      fit: 'contain',
      background: { r: 0, g: 0, b: 0, alpha: 0 },
      kernel: 'cubic',
    });

    // 3. 透かしを追加（オプション）
    if (watermark) {
      try {
        const watermarkPath = path.resolve(watermark);
        const watermarkBuffer = await fs.readFile(watermarkPath);

        // 透かし画像のサイズを確認して必要なら指定サイズにリサイズ
        const watermarkImage = sharp(watermarkBuffer);
        const watermarkMeta = await watermarkImage.metadata();

        // メタデータからの幅と高さを取得（未定義の場合はimgSizeの値を使用）
        const watermarkWidth = watermarkMeta.width ?? imgSize.width;
        const watermarkHeight = watermarkMeta.height ?? imgSize.height;

        // 透かしのサイズが異なる場合はリサイズ
        let processedWatermark;
        if (watermarkWidth !== imgSize.width || watermarkHeight !== imgSize.height) {
          processedWatermark = await watermarkImage.resize(imgSize.width, imgSize.height, { fit: 'fill' }).toBuffer();
        } else {
          processedWatermark = watermarkBuffer;
        }

        // 透かしを合成
        processedImage = processedImage.composite([{ input: processedWatermark, gravity: 'centre' }]);
      } catch (error) {
        console.error('Failed to apply watermark:', error);
      }
    }

    // 4. JPEG形式で出力してBase64エンコード
    const outputBuffer = await processedImage.jpeg({ quality }).toBuffer();

    return `data:image/jpeg;base64,${outputBuffer.toString('base64')}`;
  } catch (error) {
    console.error('Failed to generate blurred thumbnail:', error);
    throw new Error('Failed to generate blurred thumbnail');
  }
}
