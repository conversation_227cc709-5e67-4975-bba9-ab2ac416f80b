'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import Button from '@/components/atoms/button';
import RadioGroup from '@/components/atoms/radioGroup';
import RadioGroupItem from '@/components/atoms/radioGroup/radio-group-item';
import ShopPublicImage from '@/components/ShopImage';
import { useOrderFormStore } from '@/store/useOrderFormStore';
import CardInformation from '@/app/payment/_components/CardInformation';
import { PAYMENT_METHODS } from '@/consts/order';
import { useGetCards } from '@/hooks/swr/useGetCards';
import { useSelectCard } from '@/hooks/useSelectCard';
import { getOrderReturnToPath } from '@/utils/order';

type Props = {
  identityId: string;
};

const PaymentSelectionSection = ({ identityId }: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const isGacha = searchParams.get('isGacha') === 'true';
  const itemId = searchParams.get('itemId');
  const quantity = searchParams.get('quantity');

  const { setPaymentMethod, selectedCard, setSelectedCard, paymentMethod } = useOrderFormStore();
  const [value, setValue] = useState(paymentMethod ? paymentMethod : 'card');

  const { data, isLoading } = useGetCards();
  const cards = data?.data;
  const { selectCard } = useSelectCard();

  useEffect(() => {
    const selected = selectCard(cards);
    setSelectedCard(selected);
  }, [cards, setSelectedCard, selectCard]);

  const handleChange = (selectedValue: PAYMENT_METHODS) => {
    setPaymentMethod(selectedValue);
    setValue(selectedValue);
  };

  const getQueryParams = () => {
    const baseParams = `identityId=${identityId}`;
    if (isGacha) {
      return `${baseParams}&isGacha=true&itemId=${itemId}&quantity=${quantity}`;
    }
    return baseParams;
  };

  const handleGoToCardList = () => {
    const orderReturnToPath = getOrderReturnToPath(pathname, searchParams);
    router.push(`/payment/credit-card/list?returnTo=${encodeURIComponent(orderReturnToPath)}`);
  };

  return (
    <div className="flex w-full items-center justify-between gap-12">
      <div className="grow gap-4">
        <RadioGroup name="payment-method" defaultValue="card" direction="column">
          <div className="flex h-8 items-center justify-between">
            <RadioGroupItem
              label={
                isLoading ? (
                  <div className="flex animate-pulse items-center gap-2">
                    <div className="h-4 w-8 rounded bg-gray-200"></div>
                    <div className="h-4 w-24 rounded bg-gray-200"></div>
                  </div>
                ) : selectedCard ? (
                  <CardInformation
                    brand={selectedCard.name}
                    number={selectedCard.number}
                    expiresAt={selectedCard.expiresAt}
                  />
                ) : (
                  'カード情報を登録'
                )
              }
              value={PAYMENT_METHODS.CARD}
              name="payment-method"
              selectedValue={value}
              onChange={() => handleChange(PAYMENT_METHODS.CARD)}
            />
            <Button onClick={handleGoToCardList} buttonType="light-small" buttonShape="circle" buttonSize="xxs">
              <ShopPublicImage
                src="/images/icons/Arrow_Back.svg"
                width={12}
                height={12}
                alt="help"
                className="-rotate-180"
              />
            </Button>
          </div>
          <div className="flex h-8 items-center justify-between">
            <RadioGroupItem
              label="コンビニ決済"
              value={PAYMENT_METHODS.CONVENIENCE}
              name="payment-method"
              selectedValue={value}
              onChange={() => handleChange(PAYMENT_METHODS.CONVENIENCE)}
            />
            <Button
              onClick={() => router.push(`/payment/convenience-store/register?${getQueryParams()}`)}
              buttonType="light-small"
              buttonShape="circle"
              buttonSize="xxs"
            >
              <ShopPublicImage
                src="/images/icons/Arrow_Back.svg"
                width={12}
                height={12}
                alt="help"
                className="-rotate-180"
              />
            </Button>
          </div>
          <RadioGroupItem
            label={
              <div className="ml-3.5 flex h-8 items-center gap-1">
                <ShopPublicImage src="/images/icons/GooglePay.svg" alt="Google" width={30} height={20} />
                <span className="text-medium-14">GooglePay</span>
              </div>
            }
            value={PAYMENT_METHODS.GOOGLE_PAY}
            name="payment-method"
            selectedValue={value}
            onChange={() => handleChange(PAYMENT_METHODS.GOOGLE_PAY)}
          />
          {/*SafariのみApplePay選択可能*/}
          {(window as any).ApplePaySession && (
            <RadioGroupItem
              label={
                <div className="ml-3.5 flex h-8 items-center gap-1">
                  <ShopPublicImage src="/images/icons/ApplePay.svg" alt="ApplePay" width={30} height={20} />
                  <span className="text-medium-14">ApplePay</span>
                </div>
              }
              value={PAYMENT_METHODS.APPLE_PAY}
              name="payment-method"
              selectedValue={value}
              onChange={() => handleChange(PAYMENT_METHODS.APPLE_PAY)}
            />
          )}
          <RadioGroupItem
            label={
              <div className="ml-3.5 flex h-8 items-center gap-1">
                <ShopPublicImage src="/images/icons/PayPay.svg" alt="PayPay" width={80} height={20} />
              </div>
            }
            value={PAYMENT_METHODS.PAY_PAY}
            name="payment-method"
            selectedValue={value}
            onChange={() => handleChange(PAYMENT_METHODS.PAY_PAY)}
          />
        </RadioGroup>
      </div>
    </div>
  );
};

export default PaymentSelectionSection;
