'use client';
import { PurchasedItemDetail } from '@/lib/server-api/shop-api.schemas';
import { formatDateTime } from '@/utils/base';

interface PaymentReceiptProps {
  purchasedItemDetail: PurchasedItemDetail;
}

const PaymentReceipt = ({ purchasedItemDetail }: PaymentReceiptProps) => {
  const getPaymentTypeString = (paymentType: string) => {
    switch (paymentType) {
      case 'credit_card':
        return 'クレジットカード';
      case 'convenience':
        return 'コンビニ決済';
      case 'pay_pay':
        return 'PayPay';
      case 'apple_pay':
        return 'Apple Pay';
      case 'google_pay':
        return 'Google Pay';
      default:
        return '';
    }
  };

  const getPaymentStatusString = (status: string) => {
    switch (status) {
      case 'UNPROCESSED':
        return '未処理';
      case 'REQSUCCESS':
        return '支払い申込中';
      case 'PAYSUCCESS':
        return '購入済み';
      case 'EXPIRED':
        return '支払い期限切れ';
      case 'CANCEL':
        return 'キャンセル';
      case 'PAYFAILED':
        return '取引中止';
      default:
        return '';
    }
  };

  const ReceiptRow = ({ title, value }: { title: string; value: string }) => {
    return (
      <div className="flex h-6 w-full border-b-1.5 border-b-white">
        <div className="flex h-full w-20 items-center justify-center bg-gray-200 text-regular-10">{title}</div>
        <div className="flex h-full flex-1 items-center justify-start bg-gray-50 pl-2 text-regular-12">{value}</div>
      </div>
    );
  };

  const PriceRow = ({ label, price, quantity }: { label: string; price: number; quantity?: number }) => (
    <div className="flex items-center justify-between">
      <span className="truncate">{label}</span>
      <div className="flex w-20 items-center justify-between">
        <span>{quantity ? `${quantity}点` : ''}</span>
        <span>¥{price}</span>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col">
      <div className="flex w-full flex-col gap-2">
        <div>
          <ReceiptRow title={'注文日'} value={formatDateTime(purchasedItemDetail.order.orderedAt, 'jp')} />
          <ReceiptRow title={'注文番号'} value={purchasedItemDetail.order.orderNumber || 'なし'} />
        </div>
        <div>
          <ReceiptRow
            title={'お支払い方法'}
            value={getPaymentTypeString(purchasedItemDetail.checkout?.paymentType || '')}
          />
          <ReceiptRow
            title={'お支払い状況'}
            value={getPaymentStatusString(purchasedItemDetail.checkout?.status || '')}
          />
          <div className="flex min-h-6">
            <div className="flex w-20 items-center justify-center bg-gray-200 text-regular-10">お支払い金額</div>
            <div className="flex flex-1 items-center justify-center bg-gray-50 p-2 text-regular-10">
              <div className="flex w-full flex-col">
                <div className="flex flex-col gap-2 pb-1">
                  {purchasedItemDetail.order.items.map((item) => (
                    <PriceRow key={item.id} label={item.name} quantity={item.quantity || 1} price={item.price || 0} />
                  ))}
                  {purchasedItemDetail.checkout?.tipAmount && (
                    <PriceRow label="チップ" price={purchasedItemDetail.checkout.tipAmount} quantity={1} />
                  )}
                  {purchasedItemDetail.checkout?.deliveryFee && (
                    <PriceRow label="配送料" price={purchasedItemDetail.checkout.deliveryFee} />
                  )}
                  {purchasedItemDetail.checkout?.cvsFee && (
                    <PriceRow label="コンビニ手数料" price={purchasedItemDetail.checkout.cvsFee} />
                  )}
                </div>

                <div className="border-t border-gray-800" />

                <div className="pt-2">
                  <div className="flex items-center justify-end gap-6">
                    <span>合計金額:</span>
                    <span className="text-regular-13">¥{purchasedItemDetail.checkout?.total}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentReceipt;
