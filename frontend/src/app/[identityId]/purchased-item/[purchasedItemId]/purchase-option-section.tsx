import React from 'react';
import StateBadge from '@/components/atoms/badges/state-badge';
import { PurchasedItemDetail } from '@/lib/server-api/shop-api.schemas';

interface PurchaseOptionSectionProps {
  purchasedItemDetail: PurchasedItemDetail;
}

const PurchaseOptionSection = ({ purchasedItemDetail }: PurchaseOptionSectionProps) => {
  return (
    <div className="flex w-full flex-col px-4">
      {purchasedItemDetail.purchaserComment && (
        <div className="flex flex-row items-start gap-2">
          <StateBadge type="round-filled" color="white" size="sm" position="center">
            備考
          </StateBadge>
          <div className="flex-1 px-1 text-regular-13">
            <p className="text-regular-13">{purchasedItemDetail.purchaserComment}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PurchaseOptionSection;
