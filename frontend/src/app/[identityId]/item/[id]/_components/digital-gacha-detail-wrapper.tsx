'use client';
import { useEffect, useRef } from 'react';
import GachaCollectionSection from '@/components/containers/GachaCollectionSection';
import GachaCompleteProgressBar from '@/components/containers/GachaCompleteProgressBar';
import GachaCompleteSection from '@/components/containers/GachaCompleteSection';
import GachaDetailItemsSection from '@/components/containers/GachaDetailItemSection';
import GachaDetailMainInfo from '@/components/containers/GachaDetailMainInfo';
import GachaPullSection from '@/components/containers/GachaPullSection';
import ThumbnailSwiper from '@/components/containers/ThumbnailSwiper';
import DetailPageInstruction from '@/components/views/DetailPageInstruction';
import { useCheckPullableGachaStatus } from '@/hooks/use-check-pullable-gacha-status';

// FIXME any削除
type DigitalGachaDetailWrapperProps = {
  thumbnailProps: any;
  pullGachaProps: any;
  gachaMainInfoProps: any;
  gachaItemsSectionProps: any;
  gachaCompleteSectionProps: any;
  progressBarProps: any;
};

const DigitalGachaDetailWrapper = ({
  thumbnailProps,
  pullGachaProps,
  gachaMainInfoProps,
  gachaItemsSectionProps,
  gachaCompleteSectionProps,
  progressBarProps,
}: DigitalGachaDetailWrapperProps) => {
  const { itemId, identityId } = pullGachaProps;
  const { checkPullableGachaStatus } = useCheckPullableGachaStatus(Number(itemId), identityId);
  const hasCheckedPullableGachaStatus = useRef(false);

  useEffect(() => {
    if (itemId && identityId && !hasCheckedPullableGachaStatus.current) {
      checkPullableGachaStatus();
      hasCheckedPullableGachaStatus.current = true;
    }
  }, [itemId, identityId, checkPullableGachaStatus]);

  return (
    <>
      <ThumbnailSwiper props={thumbnailProps} />
      <GachaPullSection props={pullGachaProps} />
      <GachaDetailMainInfo props={gachaMainInfoProps} />
      <GachaDetailItemsSection props={gachaItemsSectionProps} />
      <GachaCompleteSection props={gachaCompleteSectionProps} />
      <GachaCollectionSection isPreview={false} itemFiles={thumbnailProps.itemFiles} />
      <GachaCompleteProgressBar props={progressBarProps} />
      <div className="mt-4">
        <DetailPageInstruction />
      </div>
    </>
  );
};

export default DigitalGachaDetailWrapper;
