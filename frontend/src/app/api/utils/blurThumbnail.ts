import { buildBlurThumb } from '@/app/actions/generateBlurWatermark';
import { GachaFile } from '@/app/api/types/backend/shop/ShopItem';
import { MediaType } from '@/types/shopItem';
/**
 * ガチャファイル用のblurサムネイルを生成する
 */
const generateBlurredThumbnails = async (files: GachaFile[], imgSize: { width: number; height: number }) => {
  const blurredFiles = await Promise.all(
    files.map(async (file) => {
      let processedThumbnail = '';
      if (file.thumbnail_uri && file.file_type !== 'audio') {
        try {
          processedThumbnail = await buildBlurThumb({
            url: file.thumbnail_uri,
            imgSize,
            watermark: 'public/images/icons/setting/MaskWhite.svg',
            quality: 70,
          });
        } catch (error) {
          console.error('Failed to generate blurred thumbnail:', error);
        }
      }

      return {
        id: String(file.id),
        title: file.name,
        type: file.file_type as MediaType,
        src: file.object_uri,
        thumbnail: file.thumbnail_uri || '',
        processedThumbnail,
        size: file.size,
        duration: file.duration,
        sortOrder: file.sort_order,
        awardType: file.award_type,
        isSecret: file.is_secret,
        receivedFileCount: file.received_file_count,
      };
    }),
  );

  return blurredFiles;
};

export { generateBlurredThumbnails };
