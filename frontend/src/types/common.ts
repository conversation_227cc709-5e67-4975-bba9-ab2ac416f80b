import { ReactNode } from 'react';
import { GLOBAL_NAVIGATION_ICON } from '@/consts/navigation';

export type HeaderInfo = {
  leftContents?: ReactNode;
  contents?: ReactNode;
  rightContents?: ReactNode;
};

export enum PageType {
  CREATE_SHOP = 'CREATE_SHOP',
  CREATE_ITEM = 'CREATE_ITEM',
  CONFIRM_ITEM = 'CONFIRM_ITEM',
  PREVIEW_ITEM = 'PREVIEW_ITEM',
  EDIT_SHOP = 'EDIT_SHOP',
  EDIT_ITEM = 'EDIT_ITEM',
  ITEM_DETAIL = 'ITEM_DETAIL',
  ITEM_VIEWER = 'ITEM_VIEWER',
  PURCHASED_ITEM = 'PURCHASED_ITEM',
  ITEM_LIST = 'ITEM_LIST',
  CART = 'CART',
  ORDER = 'ORDER',
  GACHA_ORDER = 'GACHA_ORDER',
  ORDER_SUCCESS = 'ORDER_SUCCESS',
  CONVENIENCE_STORE = 'CONVENIENCE_STORE',
  CREDIT_CARD = 'CREDIT_CARD',
  EDIT_ADDRESS = 'EDIT_ADDRESS',
  TOKUSHOHO = 'TOKUSHOHO',
  GACHA_RESULT = 'GACHA_RESULT',
  COMPS = 'COMPS',
}

export type GlobalNavigationIconKey = (typeof GLOBAL_NAVIGATION_ICON)[keyof typeof GLOBAL_NAVIGATION_ICON]['key'];
export type GlobalNavigationIconType = (typeof GLOBAL_NAVIGATION_ICON)[keyof typeof GLOBAL_NAVIGATION_ICON];
export type GlobalNavigationActiveIcon = GlobalNavigationIconKey | null;
