import React from 'react';
import Image from 'next/image';

type GachaCompleteProgressBarWithIconProps = {
  collectedUniqueItemsCount: number;
  total: number;
};

export const GachaCompleteProgressBarWithIcon = ({
  collectedUniqueItemsCount,
  total,
}: GachaCompleteProgressBarWithIconProps) => {
  // Calculate progress percentage
  const progressPercentage = Math.min(100, Math.round((collectedUniqueItemsCount / total) * 100));

  const isCompleted = collectedUniqueItemsCount === total;

  const src = isCompleted ? '/shop/images/gacha/icons/gacha_rainbow.svg' : '/shop/images/gacha/icons/gacha_gray.svg';

  return (
    <div className="flex items-center gap-1">
      <Image src={src} width={20} height={20} alt="gacha icon" />
      <div className="relative h-5 w-full rounded-m bg-gray-550">
        {/* グラデーション付きの進捗バー */}
        <div
          className="absolute left-0 top-0 h-full w-full rounded-m"
          style={{
            background:
              'linear-gradient(83.38deg, #00E69B 5.2%, #00B9E6 23.12%, #0050D2 41.04%, #8C1EE6 58.96%, #FF55AF 76.88%, #FFC814 94.8%)',
            clipPath: `inset(0 ${100 - progressPercentage}% 0 0)`,
            boxShadow: '0px 0px 8px 0px #FFFFFF inset',
          }}
        ></div>
        {/* テキストの層 */}
        <div className="relative flex h-full items-center justify-center text-white">
          {isCompleted ? (
            <span className="text-bold-14">Completed!!</span>
          ) : (
            <span className="text-regular-12">
              {collectedUniqueItemsCount} / {total}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};
