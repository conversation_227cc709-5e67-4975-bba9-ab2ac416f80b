'use client';
import clsx from 'clsx';
import Image from 'next/image';
import GoodsIcon from '@/components/atoms/badges/goods-icon';
import ShopPublicImage from '@/components/ShopImage';
import { roboto } from '@/app/fonts';
import { sortGachaFiles } from '@/utils/gacha-helpers';
import type { GachaItemFile } from '@/types/gacha';
import { AWARD_TYPE } from '@/types/gacha';

type GachaCollectionProps = {
  isPreview: boolean;
  itemFiles: GachaItemFile[];
  hasBg?: boolean;
  onItemClick?: (index: number) => void;
};

const GachaCollection = ({ isPreview, itemFiles, hasBg = true, onItemClick }: GachaCollectionProps) => {
  const sortedItemFiles = sortGachaFiles(itemFiles);

  // Calculate how many placeholder items we need to add to make the grid complete
  const placeholdersNeeded = sortedItemFiles.length % 3 === 0 ? 0 : 3 - (sortedItemFiles.length % 3);

  // Get the appropriate frame based on award type
  const getFrameSrc = (awardType: number): string => {
    switch (awardType) {
      case 4:
        return '/shop/images/gacha/GachaFrame_ThmS.svg';
      case 3:
        return '/shop/images/gacha/GachaFrame_ThmA.svg';
      case 2:
        return '/shop/images/gacha/GachaFrame_ThmB.svg';
      case 1:
        return '/shop/images/gacha/GachaFrame_ThmC.svg';
      default:
        return '/shop/images/gacha/GachaFrame_ThmC.svg';
    }
  };
  const unGetGacha = (index: number) => {
    return (
      <div
        className="flex h-29 w-29 items-center justify-center bg-[url('/shop/images/icons/circle.svg')] bg-contain bg-center bg-no-repeat"
        style={{ backgroundSize: '88px 88px', opacity: 0.2 }}
      >
        <span className="text-bold-30 text-white opacity-30">{index + 1 > 9 ? `${index + 1}` : `0${index + 1}`}</span>
      </div>
    );
  };

  // 元のitemFilesからインデックスを取得する関数
  const getOriginalIndex = (id: string) => {
    return itemFiles.findIndex((item) => item.id === id);
  };

  // Create placeholder items with noGacha.webp image
  const renderPlaceholders = () => {
    if (placeholdersNeeded === 0) return null;

    return Array.from({ length: placeholdersNeeded }).map((_, index) => (
      <ShopPublicImage
        key={`placeholder-${index}`}
        src="/images/gacha/noGacha.webp"
        alt="Empty slot"
        width={116}
        height={116}
        className="size-29 object-cover"
      />
    ));
  };

  const handleItemClick = (item: GachaItemFile) => {
    if (onItemClick && item.receivedFileCount) {
      const originalIndex = getOriginalIndex(item.id);
      if (originalIndex !== -1) {
        onItemClick(originalIndex);
      }
    }
  };

  return (
    <div className={clsx('flex items-center justify-center pb-5.5 pt-2', { 'bg-secondary': hasBg })}>
      <div className="grid grid-cols-3 gap-2">
        {sortedItemFiles.map((item, index: number) => {
          return (
            <div key={item.id || index} className="relative">
              {/* Item with frame as background */}
              <div
                className="size-29 bg-contain bg-center bg-no-repeat"
                style={{
                  backgroundImage: `url(${getFrameSrc(item.awardType || AWARD_TYPE.S)})`,
                }}
              >
                <div className="flex size-full items-center justify-center">
                  {item.receivedFileCount || isPreview ? (
                    <Image
                      src={isPreview ? item.preSignedThumbnailUrl! : item.thumbnail!}
                      alt={item.title || `Item ${index + 1}`}
                      width={110}
                      height={110}
                      className={clsx('size-27.5 rounded-sm object-cover', {
                        'cursor-pointer transition-opacity hover:opacity-80': !!onItemClick && item.receivedFileCount,
                      })}
                      onClick={() => handleItemClick(item)}
                    />
                  ) : (
                    unGetGacha(index)
                  )}
                  <GoodsIcon type={item.type || 'image'} mode="dark" className="absolute bottom-2.5 left-2.5" />
                  {item.receivedFileCount !== undefined && item.receivedFileCount > 1 && (
                    <span
                      className={clsx(
                        'absolute bottom-2.5 right-2.5 flex h-3.5 w-7 items-center justify-center rounded-lg bg-white text-medium-11 text-secondary',
                        roboto.className,
                      )}
                    >
                      x {item.receivedFileCount}
                    </span>
                  )}
                  {item.isNew && (
                    <span className="absolute left-1.5 top-1.5 flex h-3.5 w-10">
                      <ShopPublicImage src="/images/icons/newicon.svg" alt="new" width={39} height={12} />
                    </span>
                  )}
                </div>
              </div>
            </div>
          );
        })}
        {renderPlaceholders()}
      </div>
    </div>
  );
};

export default GachaCollection;
