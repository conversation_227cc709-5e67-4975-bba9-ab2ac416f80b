'use client';
import React, { useEffect, useMemo, useRef } from 'react';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import OutlinedButton from '@/components/atoms/button/outlined-button';
import { OverlayImage } from '@/components/atoms/itemIcon';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import ShopPublicImage from '@/components/ShopImage';
import GachaCompleteBadge from '../GachaCompleteBadge';
import { useExhibitsStore } from '@/store/useExhibit';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import { useHandleSetCustomThumbnail } from '@/hooks/useHandleSetCustomThumbnail';

type GachaThumbnailSectionProps = {
  numbering: string;
};

const GachaThumbnailSection = ({ numbering }: GachaThumbnailSectionProps) => {
  const { exhibits, setThumbnailCustomImage, setThumbnail } = useExhibitsStore();

  const { handleSetCustomThumbnail } = useHandleSetCustomThumbnail();
  const params = useParams();
  const itemId = params.id as string;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const { title, thumbnailCustomImage = '' } = exhibitItem ?? {};

  const customThumbnailRef = useRef<HTMLInputElement>(null);

  const handleOnUploadCustomThumbnail = () => {
    customThumbnailRef.current?.click();
  };

  const popupHeader = '表紙とコンプバッジの画像設定';
  const popupContent = (
    <div className="flex items-center justify-center bg-navy-50 py-4">
      <ShopPublicImage src={'/images/gacha/gachaThumbnailCover.webp'} width={242} height={158} alt="modal" />
    </div>
  );
  const popupFooter = 'ここで登録した画像は、商品一覧に表示されるサムネイルと、コンプリートバッジに表示されます。';
  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenIntroductionModal = () => {
    openInstructionModal(popupHeader, popupContent, popupFooter);
  };

  useEffect(() => {
    if (thumbnailCustomImage) {
      setThumbnail(itemId, thumbnailCustomImage);
    }
  }, [thumbnailCustomImage, itemId, setThumbnail]);

  return (
    <section className="section-double-border py-4">
      <div className="px-4">
        <div className="mb-4 flex items-center justify-between">
          <SectionTitleWithNumber
            title="表紙とコンプバッジの画像設定"
            numbering={numbering}
            required
            className="!mb-0"
          />
          <Button buttonType="light-small" buttonShape="circle" buttonSize="xxs" onClick={handleOpenIntroductionModal}>
            <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
          </Button>
        </div>

        <div>
          <div className="mb-4 text-medium-13">表紙専用の画像アップロード</div>
          <div className="mb-7 flex justify-center">
            {thumbnailCustomImage ? (
              <div className="relative flex size-40 items-center justify-center overflow-hidden rounded-full">
                <OutlinedButton
                  buttonColor="transparent"
                  buttonShape="circle"
                  buttonType="edit"
                  buttonSize="md"
                  className="absolute inset-0 z-10 m-auto"
                  onClick={handleOnUploadCustomThumbnail}
                />
                <OverlayImage src={`${thumbnailCustomImage}?t=${new Date().getTime()}`} />
                <Image
                  src={`${thumbnailCustomImage}?t=${new Date().getTime()}`}
                  alt="thumbnail"
                  width={160}
                  height={160}
                  className="absolute size-40 object-contain"
                />
              </div>
            ) : (
              <div
                className="relative flex size-40 items-center justify-center overflow-hidden rounded-full bg-[url('/shop/images/empty.svg')] bg-repeat"
                onClick={handleOnUploadCustomThumbnail}
              >
                <Button buttonType="light-small" buttonShape="circle" buttonSize="sm">
                  <ShopPublicImage src="/images/icons/Plus.svg" width={17} height={17} alt="upload file" />
                </Button>
              </div>
            )}
            <input
              type="file"
              name="thumb"
              accept="image/jpeg, image/png, image/gif, image/jpg"
              ref={customThumbnailRef}
              hidden
              onChange={async (e: React.ChangeEvent<HTMLInputElement>) => {
                await handleSetCustomThumbnail(e, {
                  itemId,
                  title,
                  setThumbnailCustomImage,
                });

                e.target.value = ''; // 同じファイルを選択してもonChangeが発火させるようクリア
              }}
            />
          </div>
          <div className="mb-4">
            <div className="text-medium-13">コンプリートバッジ画像の設定</div>
            <span className="text-regular-10 text-gray-500">*ガチャ図鑑コンプリートでユーザーが貰えるバッジです</span>
          </div>
          <GachaCompleteBadge
            creatorAvatar={thumbnailCustomImage && `${thumbnailCustomImage}?t=${new Date().getTime()}`}
            // 出品流れにコンプバッジのランクがいらない,0にする
            completeRank={0}
          />
        </div>
      </div>
    </section>
  );
};

export default GachaThumbnailSection;
