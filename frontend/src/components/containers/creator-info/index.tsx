'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';

interface CreatorInfoProps {
  creatorName: string;
  identityId: string;
  creatorIcon?: string;
}

const CreatorInfo = ({ creatorName, identityId, creatorIcon }: CreatorInfoProps) => {
  const router = useRouter();

  const handleClick = () => {
    router.push(`${process.env.NEXT_PUBLIC_FANME_LINK_URL}/@${identityId}`);
  };

  return (
    <div className="flex cursor-pointer items-center" onClick={handleClick}>
      <div className="size-7 overflow-hidden rounded-full">
        <Image
          src={creatorIcon || '/images/icons/NoImage.svg'}
          width={28}
          height={28}
          className="object-cover"
          alt="creator icon"
        />
      </div>
      <div className="flex flex-col pl-1">
        <p className="pl-0.5 text-bold-12">{creatorName}</p>
        {identityId && <p className="text-regular-8 text-gray-200">@{identityId}</p>}
      </div>
    </div>
  );
};

export default CreatorInfo;
