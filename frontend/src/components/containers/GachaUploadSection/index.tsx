'use client';
import React, { useRef } from 'react';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import GachaProbabilitySettings from './gacha-probability-settings';
import GachaUploadItems from './gacha-upload-items';

type GachaUploadSectionProps = {
  numbering: string;
  shopLimitation: ShopLimitation;
  isEdit?: boolean;
};

const GachaUploadSection = ({ numbering, shopLimitation, isEdit }: GachaUploadSectionProps) => {
  // 合計ガチャ数セクションの参照を作成
  const totalGachaCountSectionRef = useRef<HTMLDivElement>(null);

  return (
    <div>
      {/* アップロードコンポーネントと確率設定コンポーネントの両方に同じrefを渡す */}
      <GachaUploadItems
        numbering={numbering}
        shopLimitation={shopLimitation}
        totalGachaCountSectionRef={totalGachaCountSectionRef}
        isEdit={isEdit}
      />
      <GachaProbabilitySettings totalGachaCountSectionRef={totalGachaCountSectionRef} isEdit={isEdit} />
    </div>
  );
};

export default GachaUploadSection;
