'use client';

import React, { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import clsx from 'clsx';
import { useParams, useSearchParams } from 'next/navigation';
import Accordion from '@/components/atoms/accordion';
import Button from '@/components/atoms/button';
import DateInput from '@/components/atoms/inputs/date-input';
import NumberInput from '@/components/atoms/inputs/number-input';
import TextInput from '@/components/atoms/inputs/text-input';
import Instruction from '@/components/atoms/typography/instruction';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import ShopPublicImage from '@/components/ShopImage';
import { useExhibitsStore } from '@/store/useExhibit';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import AccordionContent from './accordion-content';
import SelectIsDuplicated from './select-is-duplicated';
import { IsDuplicated } from './select-is-duplicated';
import SingleSaleBlock from './single-sale-block';
import { roboto } from '@/app/fonts';
import {
  ITEM_MAX_SALES_COUNT,
  ITEM_MIN_PRICE,
  ITEM_PASSWORD_MAX_LENGTH,
  ITEM_PASSWORD_MIN_LENGTH,
} from '@/consts/inputLength';
import { formatDateTime } from '@/utils/base';
import { isDigitalBundle, isDigitalItem, isDigitalGacha } from '@/utils/itemTypes';
import type { ExhibitGachaItem } from '@/types/exhibitItem';
import { ITEM_TYPE } from '@/types/item';
import { type ExhibitType, OptionType } from '@/types/shopItem';

type ItemOptionsType = {
  limited?: number;
  password?: string;
  percentage: number;
  discountStart?: Date;
  discountEnd?: Date;
};

type ItemOptionsProps = {
  numbering: string;
  isEdit?: boolean;
};

const ItemOptionsSection = ({ numbering, isEdit }: ItemOptionsProps) => {
  const useExhibits = useExhibitsStore();
  const {
    exhibits,
    isNew,
    setItemFiles,
    setLimited,
    setPeriod,
    setPassword,
    setDiscount,
    setSingleSale,
    setIsDuplicated,
    validatePeriodStart,
    validatePeriodEnd,
    validateDiscount,
    validateDiscountStart,
    validateDiscountEnd,
    validateLimited,
    validatePassword,
  } = useExhibits;
  const params = useParams();
  const searchParams = useSearchParams();
  const itemId = params.id as string;
  const itemType = (searchParams.get('item_type') as ExhibitType) || 'digitalBundle';
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const { percentage, start: discountStart, end: discountEnd } = exhibitItem?.discount || {};
  const [passwordLength, setPasswordLength] = useState<number | undefined>(
    isDigitalGacha(exhibitItem) ? undefined : exhibitItem?.password?.length,
  );

  // 型に応じてパスワードを取得
  const itemPassword = isDigitalGacha(exhibitItem) ? undefined : exhibitItem?.password;

  const { period, limited, priceSet = 0, discount } = exhibitItem ?? {};
  // isDuplicated は ExhibitGachaItem に特有の属性
  const isDuplicated = (exhibitItem as ExhibitGachaItem)?.isDuplicated
    ? IsDuplicated.DUPLICATE
    : IsDuplicated.NO_DUPLICATE;
  // チェキの場合はitemFilesは存在しない
  const itemFiles = isDigitalItem(exhibitItem) ? exhibitItem.itemFiles : undefined;

  const singleSale = isDigitalBundle(exhibitItem) ? exhibitItem.singleSale : false;
  const { control, watch } = useForm<ItemOptionsType>({
    mode: 'onBlur',
    defaultValues: {
      limited: exhibitItem?.limited,
      password: itemPassword,
      percentage: exhibitItem?.discount?.percentage,
      discountStart: discountStart ? new Date(discountStart) : undefined,
      discountEnd: discountEnd ? new Date(discountEnd) : undefined,
    },
  });
  const [minEndDate, setMinEndDate] = useState<string>('');
  const [minDiscountEndDate, setMinDiscountEndDate] = useState<string>('');
  const [isOptionSectionOpen, setIsOptionSectionOpen] = useState<boolean | null>(null);
  // 単品販売の初期提案価格として「商品価格/商品数」を初期設定（計算できない場合は500円を提示）
  const baseDefaultPrice = itemFiles?.length ? Math.floor(priceSet / itemFiles.length) : 500;
  const defaultPrice = baseDefaultPrice < ITEM_MIN_PRICE ? ITEM_MIN_PRICE : baseDefaultPrice; // 最低価格を下回る場合は最低価格を提示

  const popupHeader = (optionType: OptionType) => {
    switch (optionType) {
      case OptionType.SINGLE_SALE:
        return '単品販売の設定';
      case OptionType.LIMITED:
        return '販売個数の設定';
      case OptionType.PERIOD:
        return '販売期間の設定';
      case OptionType.DISCOUNT:
        return 'セール販売の設定';
      case OptionType.PASSWORD:
        return 'パスワードの設定';
      case OptionType.DUPLICATE:
        return '景品の重複設定';
      default:
        break;
    }
  };
  const popupContent = (optionType: OptionType) => {
    let content = <ShopPublicImage src={'/images/tanpin1.webp'} width={224} height={152} alt="modal" />;
    switch (optionType) {
      case OptionType.SINGLE_SALE:
        content = <ShopPublicImage src={'/images/tanpin1.webp'} width={224} height={152} alt="modal" />;
        break;
      case OptionType.LIMITED:
        content = <ShopPublicImage src={'/images/kosu.webp'} width={260} height={144} alt="modal" />;
        break;
      case OptionType.PERIOD:
        content = <ShopPublicImage src={'/images/kikan.webp'} width={218} height={144} alt="modal" />;
        break;
      case OptionType.DISCOUNT:
        content = <ShopPublicImage src={'/images/sale.webp'} width={182} height={144} alt="modal" />;
        break;
      case OptionType.PASSWORD:
        content = <ShopPublicImage src={'/images/ps.webp'} width={274} height={146} alt="modal" />;
        break;
      case OptionType.DUPLICATE:
        content = <ShopPublicImage src={'/images/duplicate.webp'} width={274} height={146} alt="modal" />;
        break;
      default:
        break;
    }
    return <div className="flex items-center justify-center gap-1 bg-navy-50">{content}</div>;
  };
  const popupFooter = (optionType: OptionType) => {
    switch (optionType) {
      case OptionType.SINGLE_SALE:
        return (
          <>
            商品データのアップロード数が2点以上の場合、
            <br />
            ページ下にある【06】の項目、「単品販売」を設定することで商品データごとに販売できます。設定をしない場合は、セット販売での出品となります。
          </>
        );
      case OptionType.LIMITED:
        return (
          <>
            <p className="mb-4">
              販売個数を設定できます。
              <br />
              （単品販売を指定しない場合のみ）
            </p>
            <p>
              単品販売を設定しない場合、
              <br />
              （商品アップロード数が1点の場合でも）
              <br />
              セット販売として出品されます。
            </p>
          </>
        );
      case OptionType.PERIOD:
        return (
          <>
            <p className="mb-4">
              販売期間を未来に設定すると
              <br />
              設定日時に商品の販売が開始されます。
            </p>
            <p>
              ※設定日時になるまで設定商品は
              <br />
                 第三者から見ることができません。
            </p>
          </>
        );
      case OptionType.PASSWORD:
        return (
          <>
            パスワードは購入者が購入する際にパスワードを求められる機能です。特定の人にのみ販売したい場合などに有効な機能です。
          </>
        );
      case OptionType.DISCOUNT:
        return (
          <>
            <p className="mb-4">設定した割引率と期間で割引販売が実施できます。</p>
            <p>
              ※セール表記は設定した日時、期間のみ <br />
              表示されます。事前に表示されることはありません。
            </p>
          </>
        );
      default:
        break;
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSingleSale(itemId, !!e.target.checked);
    if (e.target.checked === false) return;
    // 有効にした直後は全て選択状態とする（）初期提案価格も設定
    let tempFiles = [...(itemFiles || [])];
    tempFiles = tempFiles.map((tempFile) => {
      return { ...tempFile, isSingleSale: true, price: defaultPrice };
    });
    setItemFiles(itemId, tempFiles);
  };

  useEffect(() => {
    if (period?.start || period?.end || limited || itemPassword || singleSale || discount?.percentage) {
      setIsOptionSectionOpen(true);
    } else {
      setIsOptionSectionOpen(false);
    }
  }, [period, limited, itemPassword, singleSale, discount]);

  useEffect(() => {
    const limitedValue = watch('limited');
    const passwordValue = watch('password');
    const percentageValue = watch('percentage');
    if (limitedValue !== undefined) {
      const limited = Number(limitedValue);
      if (limited < 0) {
        setLimited(itemId, 0);
        return;
      }
      setLimited(itemId, limited);
    }
    setPasswordLength(passwordValue ? passwordValue.length : 0);
    if (percentageValue) {
      setDiscount(itemId, { ...discount, percentage: Number(percentageValue) });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watch('limited'), watch('password'), watch('percentage')]);

  const handleBlurPassword = () => {
    setPassword(itemId, watch('password') || '');
  };
  const handleSaleOptionBlur = () => {
    const salePersentage = watch('percentage');
    const value = Number(salePersentage) < 0 ? 0 : Number(salePersentage);
    setDiscount(itemId, { ...discount, percentage: value });
    console.log(value);

    if (value === 0) {
      setDiscount(itemId, { ...exhibitItem?.discount, percentage: 0, start: undefined, end: undefined });
    } else {
      setDiscount(itemId, { ...discount, percentage: value });
    }
  };

  const setStartDate = (date?: Date) => {
    setPeriod(itemId, { ...exhibitItem?.period, start: date });
  };
  const setEndDate = (date?: Date) => {
    setPeriod(itemId, { ...exhibitItem?.period, end: date });
  };
  const validateDate = (data?: Date) => {
    if (!data) return;
    setMinEndDate(formatDateTime(data));
  };
  const validateDiscountDate = (data?: Date) => {
    if (!data) return;
    setMinDiscountEndDate(formatDateTime(data));
  };
  const setDiscountStartDate = (date?: Date) => {
    if (exhibitItem?.discount?.percentage) {
      setDiscount(itemId, { ...exhibitItem?.discount, start: date });
    }
  };
  const setDiscountEndDate = (date?: Date) => {
    if (exhibitItem?.discount?.percentage) {
      setDiscount(itemId, { ...exhibitItem?.discount, end: date });
    }
  };

  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenIntroductionModal = (type: OptionType) => {
    openInstructionModal(popupHeader(type), popupContent(type), popupFooter(type));
  };

  const onChangeDuplicate = (value: string) => {
    const booleanValue = value === IsDuplicated.DUPLICATE;
    setIsDuplicated(itemId, booleanValue);
  };

  const discountedPrice =
    priceSet - (priceSet * (percentage && percentage >= 1 && percentage <= 99 ? percentage : 0)) / 100;

  useEffect(() => {
    setMinEndDate(formatDateTime(period?.start));
  }, [period?.start]);

  if (isOptionSectionOpen === null) return null;
  return (
    <section className="section-double-border py-4">
      <div className="px-4">
        <SectionTitleWithNumber title="オプション設定" numbering={numbering} />
        {itemType === 'digitalBundle' && isDigitalBundle(exhibitItem) && (
          <SingleSaleBlock
            singleSale={singleSale}
            limited={!!limited}
            handleChange={handleChange}
            handleOpenIntroductionModal={handleOpenIntroductionModal}
            isNew={isNew}
            exhibitItem={exhibitItem}
            period={period}
            defaultPrice={defaultPrice}
            itemId={itemId}
          />
        )}
        {itemType !== ITEM_TYPE.DIGITAL_GACHA.str && (
          <div className="mb-4">
            <Accordion title="販売個数" type="white" defaultOpen={!!limited}>
              {!singleSale ? (
                <AccordionContent>
                  <div className="flex items-start justify-between">
                    <p className="mb-4 flex items-center gap-x-2 text-medium-14">
                      <ShopPublicImage src="/images/icons/Stock.svg" alt="calendar" width={20} height={20} />
                      販売個数を指定する
                    </p>
                    <Button
                      buttonType="light-small"
                      buttonShape="circle"
                      buttonSize="xxs"
                      onClick={() => handleOpenIntroductionModal(OptionType.LIMITED)}
                    >
                      <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
                    </Button>
                  </div>

                  <p className="mb-2 text-regular-13">
                    最大{ITEM_MAX_SALES_COUNT.toLocaleString()}個までの限定販売が出来ます。
                  </p>
                  <Instruction>*1度設定するとそれより少ない数には変更できません。</Instruction>
                  <NumberInput
                    labelTitle="販売個数"
                    suffix="個"
                    control={control}
                    inputName="limited"
                    disabled={isEdit}
                    min={0}
                    max={ITEM_MAX_SALES_COUNT}
                    defaultValue={limited}
                    errorMsg={`販売個数は${ITEM_MAX_SALES_COUNT.toLocaleString()}個以下で指定してください`}
                    inputClassName="text-bold-22 bg-gray-50 col-span-2 min-w-48"
                    className="!mb-0 mt-4 grid grid-cols-3 items-center gap-x-7"
                    error={!validateLimited(itemId)}
                    coverValue={limited}
                  />
                </AccordionContent>
              ) : (
                <div className="p-2">
                  <div className="relative flex h-14 w-full items-center justify-center rounded-lg p-4">
                    <p className="text-regular-13 text-error">
                      単体販売を許可すると
                      <br />
                      販売個数は設定できません
                    </p>
                    <Button
                      buttonType="light-small"
                      buttonShape="circle"
                      buttonSize="xxs"
                      onClick={() => handleOpenIntroductionModal(OptionType.SINGLE_SALE)}
                      className="absolute bottom-0 right-0 top-2.5"
                    >
                      <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
                    </Button>
                  </div>
                </div>
              )}
            </Accordion>
          </div>
        )}
        {itemType !== ITEM_TYPE.CHEKI.str && (
          <div className="mb-4">
            <Accordion title="販売期間" type="white" defaultOpen={!!period?.start || !!period?.end}>
              <AccordionContent>
                <div className="flex items-start justify-between">
                  <p className="mb-4 mt-2 flex items-center gap-x-2 text-medium-14">
                    <ShopPublicImage src="/images/icons/Calendar.svg" alt="calendar" width={20} height={20} />
                    販売期間を指定する
                  </p>
                  <Button
                    buttonType="light-small"
                    buttonShape="circle"
                    buttonSize="xxs"
                    onClick={() => handleOpenIntroductionModal(OptionType.PERIOD)}
                  >
                    <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
                  </Button>
                </div>

                <p className="mb-4 mt-2 text-regular-13">設定期間のみショップに表示されます。</p>
                <DateInput
                  name="saleStartDate"
                  label="販売開始"
                  className="mb-4"
                  min={formatDateTime()}
                  max={undefined}
                  errorMsg="販売開始日は現在より後の日付を指定してください"
                  customError={!validatePeriodStart(itemId)}
                  setTime={setStartDate}
                  onChange={validateDate}
                  errorClassName="col-start-1 col-end-4"
                  defaultData={period?.start?.toLocaleString()}
                />
                <DateInput
                  name="saleEndDate"
                  label="販売終了"
                  errorClassName="col-start-1 col-end-4"
                  min={period?.start ? minEndDate : ''}
                  max={undefined}
                  errorMsg={
                    period?.start
                      ? '販売終了日は販売開始日より後の日付を指定してください'
                      : '販売終了日は現在より後の日付を指定してください'
                  }
                  setTime={setEndDate}
                  customError={!validatePeriodEnd(itemId)}
                  defaultData={period?.end?.toLocaleString()}
                />
              </AccordionContent>
            </Accordion>
          </div>
        )}
        <div className="mb-4">
          <Accordion title="セール販売" type="white" defaultOpen={!!discount?.percentage}>
            <span
              className="cursor-pointer pl-4 pt-2 text-center text-regular-12 text-blue-160 underline"
              onClick={() => handleOpenIntroductionModal(OptionType.DISCOUNT)}
            >
              設定期間のみショップに表示されます。
            </span>
            {!priceSet || priceSet <= 0 ? (
              <div className="p-2">
                <p className="flex h-14 items-center justify-center bg-gray-50 text-regular-13 text-gray-500">
                  販売価格が設定されていません
                </p>
              </div>
            ) : (
              <div className="p-4">
                <NumberInput
                  control={control}
                  min={0}
                  max={99}
                  step={1}
                  labelTitle="割引率"
                  inputName="percentage"
                  defaultValue={percentage}
                  error={!validateDiscount(itemId)}
                  errorMsg={`割引率は 1〜99%、かつ割引後価格が ¥${ITEM_MIN_PRICE} 以上となるよう設定してください`}
                  className="!mb-0 grid grid-cols-4 items-center gap-x-7"
                  labelClassName="col-span-1"
                  inputClassName="text-bold-22 text-green-300 col-span-3"
                  errorMsgClassName="col-start-1 col-end-5"
                  suffix="%OFF"
                  suffixClassName={`text-green-300 text-medium-15 ml-px ${roboto.className}`}
                  coverValue={percentage && Number(percentage) > 0 ? percentage : 0}
                  onBlur={handleSaleOptionBlur}
                />
                <ul className="mb-4 grid grid-cols-4 items-center gap-x-7">
                  <li className="col-span-3 col-start-2 mt-2 flex min-w-56 items-center justify-between">
                    <span className="text-regular-13">出品価格</span>
                    <span className={clsx(roboto.className, 'text-medium-17')}>
                      ¥{Number(priceSet).toLocaleString()}
                    </span>
                  </li>
                  <li className="col-span-3 col-start-2 mt-2 flex min-w-56 items-center justify-between">
                    <span className="text-regular-13">割引後価格</span>
                    <span className={clsx(roboto.className, 'text-medium-17 text-green-300')}>
                      ¥{Number(discountedPrice.toFixed(0)).toLocaleString()}
                    </span>
                  </li>
                </ul>
                <DateInput
                  name="discountStartDate"
                  label="セール開始"
                  className="mb-4 !grid-cols-4 gap-x-7"
                  min={period?.start ? formatDateTime(period.start) : undefined}
                  max={period?.end ? formatDateTime(period.end) : undefined}
                  errorMsg="販売期間内の日付を指定してください"
                  customError={!validateDiscountStart(itemId)}
                  setTime={setDiscountStartDate}
                  onChange={validateDiscountDate}
                  disabled={!percentage || Number(percentage) > 99 || Number(percentage) < 1}
                  inputClassName="col-span-3"
                  labelClassName="col-span-1 min-w-20"
                  errorClassName="col-start-1 col-end-5"
                  defaultData={discount?.start?.toLocaleString()}
                />
                <DateInput
                  name="discountEndDate"
                  label="セール終了"
                  min={period?.start ? minEndDate : discountStart ? minDiscountEndDate : undefined}
                  max={period?.end ? formatDateTime(period.end) : undefined}
                  errorMsg={'販売期間内の日付を指定してください'}
                  customError={!validateDiscountEnd(itemId)}
                  customErrorMsg={discount?.end ? '終了日は開始日より後の日付を指定してください' : '終了日は必須です'}
                  setTime={setDiscountEndDate}
                  disabled={!percentage || Number(percentage) > 99 || Number(percentage) < 1}
                  className="!grid-cols-4 gap-x-7"
                  inputClassName="col-span-3"
                  labelClassName="col-span-1 min-w-20"
                  errorClassName="col-start-1 col-end-5"
                  defaultData={discount?.end?.toLocaleString()}
                />
              </div>
            )}
          </Accordion>
        </div>
        {/* DIGITAL_GACHA 景品の重複設定 */}
        {itemType === ITEM_TYPE.DIGITAL_GACHA.str && (
          <div className="mb-4">
            <SelectIsDuplicated isDuplicated={isDuplicated} onChangeDuplicate={onChangeDuplicate} disabled={isEdit} />
          </div>
        )}
        {itemType !== ITEM_TYPE.DIGITAL_GACHA.str && (
          <Accordion title="パスワード" type="white" defaultOpen={!!itemPassword}>
            <AccordionContent>
              <div className="mb-2 flex items-center justify-between">
                <p className="text-medium-14">購入時のパスワード付き販売ができます。</p>
                <Button
                  buttonType="light-small"
                  buttonShape="circle"
                  buttonSize="xxs"
                  onClick={() => handleOpenIntroductionModal(OptionType.PASSWORD)}
                >
                  <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
                </Button>
              </div>
              <Instruction>
                *パスワードを設定すると、商品をカートに入れる時にパスワードを入力する必要があります。
              </Instruction>
              <TextInput
                inputName="password"
                error={!validatePassword(itemId)}
                labelTitle="パスワード"
                errorMsg={`パスワードは${ITEM_PASSWORD_MIN_LENGTH}文字以上、${ITEM_PASSWORD_MAX_LENGTH}文字以内で指定してください`}
                labelClassName="mt-4"
                inputClassName="text-bold-18 !bg-gray-50"
                control={control}
                defaultValue={itemPassword}
                maxLength={ITEM_PASSWORD_MAX_LENGTH}
                textLength={passwordLength}
                showMaxLength
                onBlur={handleBlurPassword}
              />
            </AccordionContent>
          </Accordion>
        )}
      </div>
    </section>
  );
};

export default ItemOptionsSection;
