import { useState } from 'react';
import { Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import Accordion from '@/components/atoms/accordion';
import RadioGroup from '@/components/atoms/radioGroup';
import RadioGroupItem from '@/components/atoms/radioGroup/radio-group-item';
import Instruction from '@/components/atoms/typography/instruction';
import 'swiper/css';
import 'swiper/css/pagination';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import AccordionContent from './accordion-content';

export const IsDuplicated = {
  DUPLICATE: 'duplicate',
  NO_DUPLICATE: 'noDuplicate',
} as const;

type SelectIsDuplicateProps = {
  isDuplicated: (typeof IsDuplicated)[keyof typeof IsDuplicated];
  onChangeDuplicate: (value: string) => void;
  disabled?: boolean;
};

const DuplicateInstructionContent = () => {
  const [slideIndex, setSlideIndex] = useState(0);

  return (
    <>
      <div className="flex h-18 items-center justify-center text-center text-bold-15">
        {slideIndex === 0 && <div>景品の重複設定</div>}
        {slideIndex === 1 && <div>景品の重複設定 (2/2)</div>}
      </div>
      <Swiper
        pagination={{
          clickable: true,
        }}
        modules={[Pagination]}
        className="instructions-swiper !h-auto !pb-5"
        onSlideChange={(swiper) => setSlideIndex(swiper.activeIndex)}
      >
        <SwiperSlide>
          <div className="!flex flex-col items-center justify-center bg-navy-50 px-4 py-2 text-medium-14">
            <div className="mb-4 text-left">
              <p className="mb-2">❶ 「重複して出現する」設定</p>
              <p>
                同じ絵柄の景品が複数回出現する可能性があります。そのため、複数回購入するメリットとして、特典の設定をオススメします。
              </p>
            </div>
            <div className="text-left">
              <p className="mb-2">❷ 重複して出現しない</p>
              <p>同じ絵柄が2回出ることはありませんので、必ずコンプリートできます。</p>
            </div>
            {/* <ShopPublicImage src={'/images/duplicate-1.webp'} width={308} height={116} alt="modal" /> */}
          </div>
        </SwiperSlide>
        {/* secondで 出すので、コメントアウト */}
        {/* <SwiperSlide>
          <div className="!flex !h-auto flex-col items-center justify-center bg-navy-50 p-4 text-medium-14">
            <div className="mb-2 text-left">
              <p className="mb-1">♦「景品にシリアルナンバーを印字する」設定</p>
              <p>
                「重複して出現する」設定をしたときのみ選択できるオプションです。設定すると、出現した景品に重複しない番号が表示されます。
              </p>
            </div>
          </div>
        </SwiperSlide> */}
      </Swiper>
    </>
  );
};

const SelectIsDuplicated = ({ isDuplicated, onChangeDuplicate, disabled }: SelectIsDuplicateProps) => {
  const isOpen = isDuplicated === IsDuplicated.NO_DUPLICATE;
  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenDuplicateModal = () => {
    openInstructionModal(null, <DuplicateInstructionContent />, null);
  };

  const duplicateLabel = (
    <div className="ml-2">
      <p>同じ景品が重複して出現する</p>
      <Instruction>同じ景品が出現する可能性があります。</Instruction>
    </div>
  );
  const noDuplicateLabel = (
    <div className="ml-2">
      <p>重複して出現しない</p>
      <Instruction>同じ景品は出現しません。</Instruction>
    </div>
  );
  return (
    <Accordion title="景品の重複設定" type="white" defaultOpen={isOpen}>
      <div
        className="cursor-pointer pl-4 pt-2 text-regular-12 text-blue-160 underline"
        onClick={handleOpenDuplicateModal}
      >
        景品の重複設定をする。
      </div>
      <AccordionContent>
        <RadioGroup name="isDuplicate" defaultValue={IsDuplicated.DUPLICATE} direction="column">
          <>
            <RadioGroupItem
              label={duplicateLabel}
              value={IsDuplicated.DUPLICATE}
              name="isDuplicate"
              selectedValue={isDuplicated}
              onChange={onChangeDuplicate}
              disabled={disabled}
            />
            <RadioGroupItem
              label={noDuplicateLabel}
              value={IsDuplicated.NO_DUPLICATE}
              name="isDuplicate"
              selectedValue={isDuplicated}
              onChange={onChangeDuplicate}
              disabled={disabled}
            />
          </>
        </RadioGroup>
      </AccordionContent>
    </Accordion>
  );
};

export default SelectIsDuplicated;
