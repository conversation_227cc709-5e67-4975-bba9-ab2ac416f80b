'use client';
import { useRef } from 'react';
import { useRouter } from 'next/navigation';
import ReadMoreButton from '@/components/containers/ReadMoreButton';
import FixedBar from '@/components/layouts/FixedBar';
import GachaPullButtonGroup from './gacha-pull-button-group';
import { GACHA_SINGLE_PULL_COUNT, GACHA_MULTI_PULL_COUNT } from '@/consts/gacha-data';
import { useIsFixedAtBottom } from '@/hooks/useIsFixedAtBottom';
import { usePeriodState } from '@/hooks/usePeriodState';
import { getIsNowOnSale } from '@/utils/item';
import { Period, PeriodState, Discount } from '@/types/shopItem';
type GachaPullSectionProps = {
  description?: string;
  price: number;
  discount?: Discount;
  period?: Period;
  available: boolean;
  itemId: string;
  identityId: string;
  isPreview?: boolean;
  isDuplicated: boolean;
  isCompleted: boolean;
  remainingUniquePullCount: number;
};
const getIsValid = (available: boolean, periodState?: PeriodState, isPreview?: boolean) => {
  if (isPreview) return true;
  else if (!isPreview && !available) return false;
  if (periodState === PeriodState.Ended || periodState === PeriodState.BeforeStart) return false;
  else return true;
};
const GachaPullSection = ({ props }: { props: GachaPullSectionProps }) => {
  const {
    description,
    price,
    discount,
    period,
    available,
    itemId,
    identityId,
    isCompleted,
    isDuplicated,
    remainingUniquePullCount,
    isPreview,
  } = props;
  const midDiv = useRef<HTMLDivElement>(null);
  const isFixed = useIsFixedAtBottom(midDiv, 'top');
  const router = useRouter();

  const { periodState } = usePeriodState(period);

  const isValid = getIsValid(available, periodState, isPreview);

  const singleCount = GACHA_SINGLE_PULL_COUNT;
  const multiCount = isDuplicated ? GACHA_MULTI_PULL_COUNT : Math.min(GACHA_MULTI_PULL_COUNT, remainingUniquePullCount);

  const TopText = (times: number) => (
    <div className="text-extra-bold-20">
      <span className="text-bold-40">{times}</span> 回引く
    </div>
  );
  const BottomText = (times: number, isDiscount?: boolean) => {
    const originalPrice = price * times;
    if (isDiscount) {
      const isOnSale = getIsNowOnSale({
        startAt: discount?.start,
        endAt: discount?.end,
        discountRate: discount?.percentage,
      });
      if (isOnSale && discount?.percentage) {
        const discountedPrice = Number((originalPrice - (discount.percentage / 100) * price * times).toFixed(0));

        return (
          <div className="flex items-center justify-center gap-1.5 text-white">
            <span className="text-regular-14 text-green-150 line-through">¥{originalPrice.toLocaleString()}</span>
            <div className="triangle-right-white"></div>
            <span>¥{discountedPrice.toLocaleString()}</span>
          </div>
        );
      }
      return <div>¥{originalPrice.toLocaleString()}</div>;
    }
    return <div>¥{originalPrice.toLocaleString()}</div>;
  };
  const handlePull = (times: number) => {
    if (isPreview) return;
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
    const url = `${baseUrl}/@${identityId}/order?isGacha=true&itemId=${itemId}&quantity=${times}`;
    router.push(url);
  };
  const GachaPullButtons = () => {
    return (
      <GachaPullButtonGroup
        buttons={[
          {
            topText: TopText(singleCount),
            bottomText: BottomText(singleCount, !!discount?.end),
            times: singleCount,
          },
          {
            topText: TopText(multiCount),
            bottomText: BottomText(multiCount, !!discount?.end),
            times: multiCount,
          },
        ]}
        onClick={handlePull}
        disabled={!isValid}
        status={!!discount?.end ? 'green' : 'black'}
      />
    );
  };

  if (isCompleted && !isDuplicated) return null;

  return (
    <div className="pt-4">
      {/* 商品詳細 */}
      {!!description && (
        <div className="px-4 pb-6">
          <div className="whitespace-pre-wrap text-regular-13">
            <ReadMoreButton description={description} backgroundColor="#f2f2f2" />
          </div>
        </div>
      )}
      <div ref={midDiv}>
        <GachaPullButtons />
      </div>
      <FixedBar isFixed={isFixed} alignCenter={false} theme="dark" fixedHeight="h-26" regularClass="hidden">
        <GachaPullButtons />
      </FixedBar>
    </div>
  );
};

export default GachaPullSection;
