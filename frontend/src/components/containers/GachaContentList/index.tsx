'use client';
import React, { useMemo } from 'react';
import Image from 'next/image';
import AwardProbabilityDisplay from '../GachaDetailItemSection/award-probability-display';
import { useGroupGachaItems } from '@/hooks/useGroupGachaItems';
import { bytesToMB } from '@/utils/base';
import { handleAwardProbability } from '@/utils/gacha-helpers';
import { GachaItemFile, AwardProbability } from '@/types/gacha';
type GachaContentListProps = {
  itemFiles: GachaItemFile[];
  awardProbabilities?: AwardProbability[];
  totalCapacity: number;
};

/**
 * ガチャコンテンツリスト表示コンポーネント
 * S賞、A賞、B賞、C賞ごとにグループ化して表示
 */
const GachaContentList: React.FC<GachaContentListProps> = ({ itemFiles, awardProbabilities, totalCapacity = 0 }) => {
  // グループ化されたアイテムを取得
  const groupedItemsByAwardType = useGroupGachaItems(itemFiles);

  // 合計容量の表示
  const formattedCapacity = useMemo(() => {
    return bytesToMB(totalCapacity).toFixed(2);
  }, [totalCapacity]);

  return (
    <div>
      {totalCapacity > 0 && (
        <div className="mb-4">
          <span className="text-medium-12 text-secondary">合計容量: {formattedCapacity}MB</span>
        </div>
      )}

      {groupedItemsByAwardType.map((awardProb, idx) => {
        // この賞のアイテムを取得
        const items = awardProb;
        if (items.length === 0) return null;
        const { awardType, probability } = handleAwardProbability(items, awardProbabilities);
        return (
          <div key={idx} className="mb-4.5">
            <div className="mb-3">
              <AwardProbabilityDisplay awardType={awardType} probability={probability} />
            </div>

            <div className="grid grid-cols-3 gap-2">
              {items.map((item) => (
                <div key={item.id} className="relative">
                  <div className="aspect-square overflow-hidden rounded-md">
                    <Image
                      src={item.preSignedThumbnailUrl || item.thumbnail!}
                      alt={item.title || 'ガチャアイテム'}
                      className="h-full w-full object-cover"
                      width={144}
                      height={144}
                    />
                    {item.isSecret && (
                      <div className="absolute bottom-1 right-1 flex h-4 w-12 items-center justify-center rounded-lg bg-secondary text-medium-9 text-white">
                        SECRET
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default GachaContentList;
