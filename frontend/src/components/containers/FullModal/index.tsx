import React from 'react';
import Button from '@/components/atoms/button';
import ShopPublicImage from '@/components/ShopImage';

type FullModalProps = {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
};

const FullModal: React.FC<FullModalProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 mx-auto h-screen max-w-120">
      <div className="h-screen bg-gray-100">
        <div className="flex h-14 items-center justify-between bg-gray-800 px-4">
          <div className="w-8"></div>
          <div className="text-medium-14 text-white">{title}</div>
          <Button buttonSize="sm" buttonType="light-shadow" buttonShape="circle" onClick={onClose}>
            <ShopPublicImage src="/images/icons/CloseBtn.svg" width={12.83} height={12.83} alt="close" />
          </Button>
        </div>
        {children}
      </div>
    </div>
  );
};

export default FullModal;
