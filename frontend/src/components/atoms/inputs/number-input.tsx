'use client';

import React, { useRef } from 'react';
import { Control, Controller, ControllerRenderProps, FieldValues, Path } from 'react-hook-form';
import clsx from 'clsx';
import { roboto } from '@/app/fonts';

type NumberInputProps<T extends FieldValues> = {
  control: Control<T>;
  inputName: Path<T>;
  required?: boolean;
  labelTitle?: string;
  min?: number;
  max?: number;
  step?: number;
  placeholder?: string;
  defaultValue?: number | undefined;
  errorMsg?: string;
  errorMsgClassName?: string;
  error?: boolean;
  onBlur?: (value?: string | undefined) => void;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  suffixClassName?: string;
  prefix?: string;
  suffix?: string;
  coverValue?: number | string;
  shortInput?: boolean;
  disabled?: boolean;
};

const NumberInput = <T extends FieldValues>({
  control,
  inputName,
  required,
  labelTitle,
  min,
  max,
  step,
  placeholder,
  defaultValue,
  errorMsg,
  errorMsgClassName,
  error,
  onBlur,
  className,
  inputClassName,
  labelClassName,
  suffixClassName,
  prefix,
  suffix,
  coverValue,
  shortInput,
  disabled,
}: NumberInputProps<T>) => {
  const numberRef = useRef<HTMLInputElement>(null);

  const handleClickCover = () => {
    if (numberRef.current) {
      const value =
        Number(numberRef.current.value) < 0 || Number.isNaN(numberRef.current.value)
          ? 0
          : Number(numberRef.current.value);
      numberRef.current.value = value.toString();
      numberRef.current.focus();
    }
  };

  const handleOnBlur = (field: ControllerRenderProps<T, Path<T>>) => {
    field.onBlur();
    onBlur?.(field.value);
  };

  const handleKeyDownNoDecimal = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.keyCode === 110 || e.key === '.') {
      e.preventDefault();
      return;
    }
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };

  return (
    <div className={clsx('relative mb-5', className)}>
      {labelTitle && (
        <label
          htmlFor={String(inputName)}
          className={clsx(
            'mb-2 flex min-w-20 items-center justify-between text-medium-13 text-secondary',
            labelClassName,
          )}
        >
          <span>
            {labelTitle}
            {required && <span className="ml-0.5 text-regular-11 text-orange-200">*必須</span>}
          </span>
        </label>
      )}
      <Controller
        name={inputName}
        rules={{ required }}
        control={control}
        render={({ field }) => (
          <input
            {...field}
            value={field.value ?? defaultValue ?? ''}
            type="number"
            step={step}
            ref={numberRef}
            placeholder={placeholder}
            max={max}
            min={min}
            disabled={disabled}
            onBlur={handleOnBlur.bind(null, field)}
            onKeyDown={handleKeyDownNoDecimal}
            className={clsx(
              inputClassName,
              { 'min-w-48': !shortInput },
              'absolute right-0 top-0 -z-10 w-1 rounded-lg border-none bg-gray-50 px-3 text-right opacity-0 focus:z-10 focus:opacity-100',
              'h-8',
              '[appearance:textfield] [&::-webkit-inner-spin-button]:m-0 [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:m-0 [&::-webkit-outer-spin-button]:appearance-none',
            )}
          />
        )}
      />
      <div
        onClick={handleClickCover}
        className={clsx(
          inputClassName,
          'flex w-full items-center justify-end rounded-lg border-none px-3',
          'h-8',
          disabled ? 'bg-gray-200' : 'bg-gray-50',
        )}
      >
        <span
          className={clsx(roboto.className, {
            'text-regular-22 text-gray-500': !Number(coverValue),
          })}
        >
          {prefix && <span className="mr-1">{prefix}</span>}
          {Number(coverValue).toLocaleString()}
          {suffix && (
            <span className={clsx(suffixClassName ? suffixClassName : 'ml-1 text-regular-11 text-secondary')}>
              {suffix}
            </span>
          )}
        </span>
      </div>
      {error && (
        <p className={clsx('col-start-1 col-end-4 mt-1 text-right text-regular-10 text-error', errorMsgClassName)}>
          {errorMsg}
        </p>
      )}
    </div>
  );
};

export default NumberInput;
