import { GachaBenefit } from '@/types/gacha';
import { Benefit } from '@/types/shopItem';

/**
 * Extract all benefit files from either a Benefit (DigitalBundle) or GachaBenefit[] (gacha) structure.
 * @param benefits - Benefit object or GachaBenefit array or undefined
 * @returns Array of SingleItem (benefit files)
 */
// Accepts Benefit | GachaBenefit | GachaBenefit[] | undefined
export const extractBenefitFiles = (benefits: Benefit | GachaBenefit | GachaBenefit[] | undefined): any[] => {
  if (!benefits) return [];
  if (Array.isArray(benefits)) {
    // GachaBenefit[]
    return benefits.flatMap((b) => b.benefit ?? []);
  }
  if ('benefitFiles' in benefits) {
    // DigitalBundle
    return benefits.benefitFiles ?? [];
  }
  if ('benefit' in benefits) {
    // GachaBenefit (single object, fallback)
    return benefits.benefit ?? [];
  }
  return [];
};
