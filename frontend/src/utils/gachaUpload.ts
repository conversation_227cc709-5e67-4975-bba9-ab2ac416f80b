'use client';

import { toast } from 'react-hot-toast';
import CustomToast from '@/components/atoms/toast/custom-toast';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { THUMBNAIL_BLUR_LEVEL, THUMBNAIL_WATERMARK_LEVEL } from '@/consts/file';
import { MAX_GACHA_ITEM_COUNT } from '@/consts/sizes';
import { validateUploadedFiles } from '@/utils/item';
import {
  generatePreMediaThumbnail,
  generatePreSignedThumbnails,
  generateProcessedThumbnail,
  handleMediaUpload,
} from '@/utils/thumbnail';
import { GachaItemFile, Award, AWARD_TYPE } from '@/types/gacha';

export interface GachaUploadOptions {
  currentFiles: FileList | null;
  existingFiles: GachaItemFile[];
  shopLimitation: ShopLimitation;
  identityId: string;
  setFiles: (files: GachaItemFile[]) => void;
  onProgress?: (id: string, progress: number) => void;
  resetProgress?: () => void;
  awardType?: Award;
  itemId?: string;
}

/**
 * Custom handleFileUpload function for gacha uploads that preserves loading state
 */
export const handleGachaFileUpload = async (options: GachaUploadOptions): Promise<boolean> => {
  const {
    currentFiles,
    existingFiles,
    shopLimitation,
    identityId,
    setFiles,
    onProgress,
    resetProgress,
    awardType = AWARD_TYPE.S, // Default to S award
  } = options;

  // Validate input
  if (!currentFiles || currentFiles.length === 0) return false;

  // Validate files
  const validationResult = validateUploadedFiles(currentFiles, existingFiles, shopLimitation);
  if (validationResult) {
    toast.custom((t) => CustomToast(t, 'error', validationResult));
    return false;
  }

  // Check if adding these files would exceed the maximum
  if (existingFiles.length + currentFiles.length > MAX_GACHA_ITEM_COUNT) {
    toast.custom((t) => CustomToast(t, 'error', `アップロードできるファイルは最大${MAX_GACHA_ITEM_COUNT}個までです`));
    return false;
  }

  try {
    // Reset progress if provided
    if (resetProgress) {
      resetProgress();
    }

    // Create pre-upload items with isLoading=true
    const preUploadItems = Array.from(currentFiles).map((file) => {
      const preItem = generatePreMediaThumbnail(file);
      return {
        ...preItem,
        awardType,
        isSecret: false,
        sortOrder: 0,
      } as unknown as GachaItemFile;
    });

    // Add pre-upload items to existing files
    const filesWithPreUpload = [...existingFiles, ...preUploadItems];
    setFiles(filesWithPreUpload);

    // Handle the upload process with empty initial array to avoid duplicating files
    let resultFiles = await handleMediaUpload(
      currentFiles,
      [] as any,
      (files) => {
        // This callback is called during upload to show progress
        // We need to update the loading state and progress for each file
        const updatedFiles = filesWithPreUpload.map((existingFile) => {
          // Find the corresponding file in the new files
          const newFile = files.find((f) => f.id === existingFile.id);
          if (newFile) {
            // Update the file with new properties but keep isLoading=true
            return {
              ...existingFile,
              ...newFile,
              isLoading: true, // Keep isLoading=true during upload
              awardType, // Preserve award type
              isSecret: existingFile.isSecret,
              sortOrder: existingFile.sortOrder,
            };
          }
          return existingFile;
        });

        setFiles(updatedFiles);
      },
      onProgress,
    );

    // Generate thumbnails
    resultFiles = await generatePreSignedThumbnails(resultFiles, identityId);

    // Process thumbnails and set award type
    resultFiles = await Promise.all(
      resultFiles.map(async (file) => {
        const processed = await generateProcessedThumbnail(
          file as any,
          THUMBNAIL_BLUR_LEVEL.HIGH,
          THUMBNAIL_WATERMARK_LEVEL.WHITE,
        );
        return {
          ...processed,
          awardType, // Set the award type based on the current state
          isSecret: false,
          sortOrder: 0,
          isLoading: false, // Now set isLoading to false when processing is complete
        } as unknown as GachaItemFile;
      }),
    );

    // Update files in store by replacing pre-upload items with final items
    const finalFiles = existingFiles.filter((file) => !preUploadItems.some((preItem) => preItem.id === file.id));
    const updatedFiles = [...finalFiles, ...resultFiles] as GachaItemFile[];
    setFiles(updatedFiles);

    return true;
  } catch (error) {
    console.error('Error processing media files:', error);
    toast.custom((t) => CustomToast(t, 'error', 'ファイルのアップロードに失敗しました'));
    return false;
  }
};
