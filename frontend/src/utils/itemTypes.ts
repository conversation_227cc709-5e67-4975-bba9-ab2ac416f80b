import type { DigitalBundle, ExhibitChekiItem, ExhibitGachaItem } from '@/types/exhibitItem';

export const isDigitalBundle = (item: any): item is DigitalBundle => item && 'singlePrice' in item;
export const isDigitalGacha = (item: any): item is ExhibitGachaItem => item && 'awardProbabilities' in item;
export const isDigitalItem = (item: any): item is DigitalBundle | ExhibitGachaItem =>
  isDigitalBundle(item) || isDigitalGacha(item);
export const isPhysicalItem = (item: any): item is ExhibitChekiItem => !isDigitalItem(item);
