import React from 'react';
import { useRouter } from 'next/navigation';
import { getPullableGachaCount } from '@/lib/client-api/digital-gacha-endpoint/digital-gacha-endpoint';
import { useModalsStore } from '@/store/useModals';

/**
 * ガチャ注文時に未引きガチャがあるかどうかをチェックし、
 * 未引きがあればモーダルを表示するカスタムフック
 *
 * @param itemId - ガチャアイテムID
 * @param identityId - クリエイターID
 * @returns checkPullableGachaStatus関数
 */
export const useCheckPullableGachaStatus = (itemId: number, identityId: string) => {
  const { onModalOpen, setModalProps, onModalClose } = useModalsStore();
  const router = useRouter();

  const checkPullableGachaStatus = async (onCloseNavigateTo?: string): Promise<boolean> => {
    try {
      const response = await getPullableGachaCount(itemId);
      const remainingPullCount = response.data?.item?.remainingPullCount ?? 0;

      if (remainingPullCount > 0) {
        setModalProps({
          onClose: () => {
            onModalClose();
            if (onCloseNavigateTo) {
              router.push(onCloseNavigateTo);
            }
          },
          onConfirm: () => {
            router.push(`/${identityId}/item/${itemId}/result/gacha`);
            onModalClose();
          },
          confirmText: 'ガチャを引く',
          children: (
            <div className="p-6 text-center">
              <p>引いていないガチャが{remainingPullCount}回あります</p>
            </div>
          ),
        });
        onModalOpen();
        return false;
      }
      return true;
    } catch (e) {
      console.error(e);
      return false;
    }
  };

  return { checkPullableGachaStatus };
};
