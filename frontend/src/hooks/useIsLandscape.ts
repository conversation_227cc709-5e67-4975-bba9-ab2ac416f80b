import { useEffect, useState } from 'react';

export const useIsLandscape = () => {
  const isMobile =
    typeof window !== 'undefined' &&
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  const [isLandscape, setIsLandscape] = useState(
    !isMobile ? false : window.matchMedia('(orientation: landscape)').matches,
  );

  useEffect(() => {
    if (!isMobile) return;
    const mediaQuery = window.matchMedia('(orientation: landscape)');
    const handleChange = (e: MediaQueryListEvent) => {
      setIsLandscape(e.matches);
    };
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [isMobile]);

  return isLandscape;
};
