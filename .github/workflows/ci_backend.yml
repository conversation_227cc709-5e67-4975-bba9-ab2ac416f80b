name: __backend_test
run-name: CI for Backend

on:
  workflow_call:
    inputs:
      skip:
        type: string

defaults:
  run:
    working-directory: ./backend

jobs:
  ci_backend:
    runs-on: ubuntu-latest
    if: ${{ inputs.skip != 'true' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: "17"
          cache: "gradle"
          distribution: "adopt"

      - name: Lint check
        run: ./gradlew ktfmtCheck

      #      - name: Detekt check
      #        run: ./gradlew detekt

      - name: Test for Backend
        run: ./gradlew test --info
