package jp.co.torihada.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.models.AwardType
import jp.co.torihada.fanme.modules.shop.models.GachaItem
import jp.co.torihada.fanme.modules.shop.models.GachaProbability

object GachaProbabilityFactory {
    fun new(
        gachaItemId: Long,
        awardType: AwardType = AwardType.C,
        probability: Int,
    ): GachaProbability {
        return GachaProbability().apply {
            this.gachaItem = GachaItem.findById(gachaItemId)!!
            this.awardType = awardType
            this.probability = probability
        }
    }
}
