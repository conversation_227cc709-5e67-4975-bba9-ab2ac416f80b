package jp.co.torihada.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.Shop

object OrderFactory {
    fun new(
        purchaserUid: String,
        shopId: Long,
        transactionId: Long? = null,
        checkoutId: Long? = null,
    ): Order {
        return Order().apply {
            this.purchaserUid = purchaserUid
            this.shop = Shop.findById(shopId)!!
            this.transactionId = transactionId
            this.checkoutId = checkoutId
        }
    }
}
