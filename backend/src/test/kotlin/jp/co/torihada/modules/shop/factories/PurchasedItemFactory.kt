package jp.co.torihada.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem

object PurchasedItemFactory {
    fun new(
        order: Order,
        purchaserUid: String,
        itemId: Long,
        price: Int,
        quantity: Int = 1,
        purchaserComment: String? = null,
        status: String = Util.PurchasedItemStatus.REQSUCCESS.value,
    ): PurchasedItem {
        return PurchasedItem().apply {
            this.order = order
            this.purchaserUid = purchaserUid
            this.item = Item.findById(itemId)!!
            this.price = price
            this.quantity = quantity
            this.purchaserComment = purchaserComment
            this.status = status
        }
    }
}
