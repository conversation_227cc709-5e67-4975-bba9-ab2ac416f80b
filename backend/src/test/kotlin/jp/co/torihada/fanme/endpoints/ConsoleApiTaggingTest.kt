package jp.co.torihada.fanme.endpoints

import jakarta.ws.rs.Path
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.reflections.Reflections
import org.reflections.scanners.Scanners
import org.reflections.util.ClasspathHelper
import org.reflections.util.ConfigurationBuilder

class ConsoleApiTaggingTest {

    @Test
    fun `すべてのCONSOLE APIエンドポイントに正しいタグが付与されていること`() {

        val reflections =
            Reflections(
                ConfigurationBuilder()
                    .setUrls(ClasspathHelper.forPackage("jp.co.torihada.fanme.endpoints.console"))
                    .setScanners(Scanners.TypesAnnotated)
            )

        val controllers =
            reflections.getTypesAnnotatedWith(Path::class.java).filter {
                it.name.startsWith("jp.co.torihada.fanme.endpoints.console")
            }

        assertTrue(controllers.isNotEmpty(), "CONSOLEエンドポイントが見つかりません")

        controllers.forEach { controller ->
            val tags = controller.getAnnotationsByType(Tag::class.java)

            val hasCorrectTag =
                tags.any { tag -> tag.name == "CONSOLE" && tag.description == "CONSOLE APIサーバー" }

            assertTrue(
                hasCorrectTag,
                "${controller.simpleName}に@Tag(name = \"CONSOLE\", description = \"CONSOLE APIサーバー\")が付与されていません",
            )
        }
    }
}
