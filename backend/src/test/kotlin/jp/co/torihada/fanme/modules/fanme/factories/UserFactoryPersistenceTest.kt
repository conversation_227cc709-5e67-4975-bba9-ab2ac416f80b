package jp.co.torihada.fanme.modules.fanme.factories

import io.quarkus.test.junit.QuarkusTest
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.fanme.models.User
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserFactoryPersistenceTest {

    private val testUserIds = listOf("test-persistence-user-1", "test-persistence-user-2")

    @BeforeEach
    @Transactional
    fun setup() {
        User.delete("uid in (?1, ?2)", testUserIds[0], testUserIds[1])
    }

    @AfterEach
    @Transactional
    fun cleanup() {
        User.delete("uid in (?1, ?2)", testUserIds[0], testUserIds[1])
    }

    @Test
    @Transactional
    fun testUserFactoryCreate() {
        val user =
            UserFactory.createTestUser(
                uid = testUserIds[0],
                name = "Test Persistence User",
                accountIdentity = "<EMAIL>",
            )

        assertNotNull(user)
        assertEquals(testUserIds[0], user?.uid)

        val savedUser = User.find("uid", testUserIds[0]).firstResult()
        assertNotNull(savedUser)
        assertEquals("Test Persistence User", savedUser?.name)
    }

    @Test
    @Transactional
    fun testCreateDuplicateUser() {
        val firstUser =
            UserFactory.createTestUser(
                uid = testUserIds[1],
                name = "Original Name",
                accountIdentity = "<EMAIL>",
            )

        val secondUser =
            UserFactory.createTestUser(
                uid = testUserIds[1],
                name = "New Name",
                accountIdentity = "<EMAIL>",
            )

        assertNotNull(secondUser)
        assertEquals(firstUser?.id, secondUser?.id)
        assertEquals("Original Name", secondUser?.name)
        assertEquals("<EMAIL>", secondUser?.accountIdentity)

        assertEquals(1, User.count("uid", testUserIds[1]))
    }
}
