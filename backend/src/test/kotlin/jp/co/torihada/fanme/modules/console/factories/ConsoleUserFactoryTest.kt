package jp.co.torihada.fanme.modules.console.factories

import io.quarkus.test.junit.QuarkusTest
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.fanme.models.User
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ConsoleUserFactoryTest {

    private val testUid = "test-factory-uid"
    private val testName = "Test User"
    private val testEmail = "<EMAIL>"

    @BeforeEach
    @Transactional
    fun setup() {
        User.delete("uid", testUid)
    }

    @AfterEach
    @Transactional
    fun cleanup() {
        User.delete("uid", testUid)
    }

    @Test
    @Transactional
    fun testCreateTestUserNewUser() {
        val user = ConsoleUserFactory.createTestUser(testUid, testName, testEmail)

        assertNotNull(user)
        assertEquals(testUid, user?.uid)
        assertEquals(testName, user?.name)
        assertEquals(testEmail, user?.accountIdentity)
        assertEquals("MALE", user?.gender)
        assertNotNull(user?.birthday)
        assertEquals(false, user?.birthdayConfirmed)
        assertEquals(true, user?.isPublic)
        assertEquals(true, user?.filledProfile)
        assertEquals(0, user?.purpose)
    }

    @Test
    @Transactional
    fun testCreateTestUserExistingUser() {
        val firstUser = ConsoleUserFactory.createTestUser(testUid, testName, testEmail)
        assertNotNull(firstUser)

        val secondUser =
            ConsoleUserFactory.createTestUser(
                testUid,
                "Different Name",
                "<EMAIL>",
            )

        assertNotNull(secondUser)
        assertEquals(testUid, secondUser?.uid)
        assertEquals(testName, secondUser?.name)
        assertEquals(testEmail, secondUser?.accountIdentity)
        assertEquals(firstUser?.id, secondUser?.id)
    }
}
