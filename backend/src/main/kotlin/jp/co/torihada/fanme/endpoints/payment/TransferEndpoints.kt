// package jp.co.torihada.fanme.endpoints.payment
//
// import io.quarkus.security.identity.SecurityIdentity
// import jakarta.inject.Inject
// import jakarta.ws.rs.GET
// import jakarta.ws.rs.POST
// import jakarta.ws.rs.Path
// import jakarta.ws.rs.Produces
// import jakarta.ws.rs.core.Response
// import jp.co.torihada.fanme.endpoints.payment.request.CreateTransfer
// import jp.co.torihada.fanme.modules.payment.controllers.TransferController
//
// @Path("/seller/transfer")
// class TransferEndpoints {
//    @Inject private lateinit var handler: TransferController
//    @Inject lateinit var securityIdentity: SecurityIdentity
//
//    @POST
//    @Path("/bank_id")
//    @Produces("application/json")
//    fun createBankIdTransfer(): Response {
//        return try {
//            val userId = securityIdentity.attributes["login_user_uid"] as String
//            val response = handler.createBankIdTransfer(userId = userId)
//            Response.ok(response).build()
//        } catch (e: Exception) {
//            Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
//        }
//    }
//
//    @POST
//    @Produces("application/json")
//    fun createTransfer(request: CreateTransfer): Response {
//        return try {
//            val userId = securityIdentity.attributes["login_user_uid"] as String
//            val response =
//                handler.createTransfer(userId = userId, callbackUrl = request.callbackUrl)
//            Response.ok(response).build()
//        } catch (e: Exception) {
//            Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
//        }
//    }
//
//    @GET
//    @Path("/account_data")
//    @Produces("application/json")
//    fun getTransferAccount(): Response {
//        return try {
//            val userId = securityIdentity.attributes["login_user_uid"] as String
//            val response = handler.getTransferAccount(userId = userId)
//            Response.ok(response).build()
//        } catch (e: Exception) {
//            Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
//        }
//    }
//
//    @GET
//    @Produces("application/json")
//    fun getTransferAmount(): Response {
//        return try {
//            val userId = securityIdentity.attributes["login_user_uid"] as String
//            val response = handler.getTransferAmount(userId = userId)
//            Response.ok(response).build()
//        } catch (e: Exception) {
//            Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
//        }
//    }
//
//    // TODO パス名考える
//    // 使用する画面のパスをかく
//    @GET
//    @Path("/deposit")
//    @Produces("application/json")
//    fun getTransferInfo(): Response {
//        return try {
//            val userId = securityIdentity.attributes["login_user_uid"] as String
//            val response = handler.getTransferInfo(userId = userId)
//            Response.ok(response).build()
//        } catch (e: Exception) {
//            Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
//        }
//    }
// }
