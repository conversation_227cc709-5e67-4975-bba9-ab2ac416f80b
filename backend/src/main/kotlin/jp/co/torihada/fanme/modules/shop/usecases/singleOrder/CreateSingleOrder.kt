package jp.co.torihada.fanme.modules.shop.usecases.singleOrder

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.Shop

@ApplicationScoped
class CreateSingleOrder {
    data class Input(val shopId: Long, val checkoutId: Long)

    fun execute(params: Input): Result<Order, FanmeException> {
        val shop = Shop.findById(params.shopId) ?: return Err(ResourceNotFoundException("Shop"))
        val userUid = shop.creatorUid ?: return Err(ResourceNotFoundException("User"))

        val order =
            Order.create(
                purchaserUid = userUid,
                shopId = shop.id!!,
                transactionId = null,
                checkoutId = params.checkoutId,
            )

        return Ok(order)
    }
}
