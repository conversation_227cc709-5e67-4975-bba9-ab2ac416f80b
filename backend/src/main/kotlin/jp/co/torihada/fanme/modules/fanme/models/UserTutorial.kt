package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "user_tutorials")
class UserTutorial : BaseModel() {
    @NotNull
    @ManyToOne(optional = false)
    @JoinColumn(name = "creator_id", nullable = false)
    var user: User? = null

    @Size(max = 255) @NotNull @Column(name = "name", nullable = false) var name: String? = null

    @NotNull @Column(name = "display_flg", nullable = false) var displayFlg: Boolean? = false

    companion object : PanacheCompanion<UserTutorial> {
        fun findByCreatorAndName(user: User, name: String): UserTutorial? {
            return find("user = ?1 and name = ?2", user, name).firstResult()
        }
    }
}
