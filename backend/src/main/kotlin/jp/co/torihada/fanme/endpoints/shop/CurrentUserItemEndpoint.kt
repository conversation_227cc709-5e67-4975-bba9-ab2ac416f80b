package jp.co.torihada.fanme.endpoints.shop

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.ItemResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.CreateOrUpdateItemRequest
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.RequestConverter
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.SortItemsRequest
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.shop.controllers.ItemController
import jp.co.torihada.fanme.odata.OData
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/shops/current/items")
class CurrentUserItemEndpoint {

    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var requestContext: ContainerRequestContext
    @Inject lateinit var handler: ItemController
    @Inject lateinit var converter: RequestConverter
    @Inject lateinit var util: Util

    @Operation(
        operationId = "getCurrentUserItems"
    ) // MEMO: /shops/{creator_account_identity}/itemsとメソッド名が重複しているため、operationIdを指定している。
    @GET
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    fun getItems(
        @QueryParam("available") available: Boolean?,
        @QueryParam("tag") tag: String?,
    ): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val odata = requestContext.getProperty("odata") as OData?
            val result = handler.getItems(userUid, userUid, available, tag, odata, false)
            val entity = ResponseEntity(result, "items")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @POST
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun createItem(requestBody: CreateOrUpdateItemRequest): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val request = converter.requestToCreateItem(userUid, requestBody)
            val result = handler.createItem(request)
            val entity = ResponseEntity(result, "item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @Operation(operationId = "getCurrentUserItem")
    @GET
    @Path("/{item_id}")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(ItemResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getItem(
        @PathParam("item_id") id: Long,
        @QueryParam("include_deleted") includeDeleted: String?,
    ): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.getItem(id, userUid, userUid, false)
            val entity = ResponseEntity(result, "item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @PUT
    @Path("/{item_id}")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun updateItem(
        @PathParam("item_id") itemId: Long,
        requestBody: CreateOrUpdateItemRequest,
    ): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val request = converter.requestToUpdateItem(userUid, itemId, requestBody)
            val result = handler.updateItem(request)
            val entity = ResponseEntity(result, "item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @PUT
    @Path("/sort")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun sortItems(requestBody: SortItemsRequest): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val request = converter.requestToSortItems(userUid, requestBody)
            val result = handler.sortItems(request)
            val entity = ResponseEntity(result, "result")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
