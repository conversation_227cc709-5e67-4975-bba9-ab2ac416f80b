package jp.co.torihada.fanme.modules.shop.usecases.badge

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.models.BadgeType
import jp.co.torihada.fanme.modules.shop.models.GachaItemFile
import jp.co.torihada.fanme.modules.shop.models.GachaReceivedFile
import jp.co.torihada.fanme.modules.shop.models.UserBadge

@ApplicationScoped
class CreateDigitalGachaCompleteBadge {
    data class Input(val userUid: String, val itemId: Long)

    fun execute(params: Input): Result<UserBadge?, FanmeException> {
        // すでにコンプリートガチャバッチを持っているか確認
        val existingBadge =
            UserBadge.findByItemIdAndUserUidAndBadgeType(
                userUid = params.userUid,
                itemId = params.itemId,
                badgeType = BadgeType.DIGITAL_GACHA_COMPLETE,
            )
        if (existingBadge != null) {
            return Ok(existingBadge)
        }

        val gachaItemFileIds =
            GachaItemFile.findByItemId(params.itemId).map { it.itemFile.id }.toSet()

        val receivedFileIds =
            GachaReceivedFile.findByPurchaserUidAndItemId(params.userUid, params.itemId)
                .mapNotNull { it?.itemFile?.id }
                .toSet()

        // 全てのファイルを取得しているか確認
        val isAllFilesReceived = gachaItemFileIds.all { it in receivedFileIds }
        return if (isAllFilesReceived) {
            Ok(
                UserBadge.create(
                    itemId = params.itemId,
                    userUid = params.userUid,
                    badgeType = BadgeType.DIGITAL_GACHA_COMPLETE,
                )
            )
        } else {
            Ok(null)
        }
    }
}
