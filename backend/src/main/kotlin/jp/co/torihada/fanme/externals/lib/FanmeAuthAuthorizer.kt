package jp.co.torihada.fanme.externals.lib

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.util.*
import jp.co.torihada.fanme.Config

@ApplicationScoped
class FanmeAuthAuthorizer {
    @Inject private lateinit var config: Config

    fun getAuthorization(): String {
        val userName = config.authApiName()
        val password = config.authApiPass()
        val basicPass = "$userName:$password"
        return "Basic " + Base64.getEncoder().encodeToString(basicPass.toByteArray())
    }
}
