package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.Const.ContentBlockType
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.Const.CONTENT_DESCRIPTION_MAX_LENGTH
import jp.co.torihada.fanme.modules.fanme.Const.CONTENT_TITLE_MAX_LENGTH
import jp.co.torihada.fanme.modules.fanme.Const.USER_UID_MAX_LENGTH
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.CreateContentBlock
import org.hibernate.validator.constraints.URL

@ApplicationScoped
class ContentBlockController : BaseController() {
    @Inject lateinit var createContentBlock: CreateContentBlock

    data class CreateContentBlockWithDetailRequest(
        @NotBlank @Size(max = USER_UID_MAX_LENGTH) val creatorUid: String,
        val contentBlockType: ContentBlockType,
        @NotBlank @Size(max = CONTENT_TITLE_MAX_LENGTH) val title: String,
        @Size(max = CONTENT_DESCRIPTION_MAX_LENGTH) val description: String? = null,
        @Size(max = CONTENT_DESCRIPTION_MAX_LENGTH) val appDescription: String? = null,
        @URL val url: String,
        @URL val iconUrl: String? = null,
    )

    @Transactional(rollbackOn = [Exception::class])
    fun createContentBlockWithDetail(
        @Valid request: CreateContentBlockWithDetailRequest
    ): ContentBlock {
        val user = User.findByUuid(request.creatorUid) ?: throw ResourceNotFoundException("User")

        val contentBlock =
            createContentBlock
                .execute(
                    CreateContentBlock.Input(
                        contentBlockType = request.contentBlockType,
                        user = user,
                    )
                )
                .getOrThrow()

        contentBlock.contentBlockGroups.first().contentBlockDetail!!.apply {
            title = request.title
            description = request.description ?: ""
            appDescription = request.appDescription ?: ""
            url = request.url
            icon = request.iconUrl
        }

        return contentBlock
    }
}
