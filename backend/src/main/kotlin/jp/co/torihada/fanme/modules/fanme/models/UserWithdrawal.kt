package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "creator_withdrawals")
class UserWithdrawal : BaseModel() {
    @NotNull
    @OneToOne(optional = false)
    @JoinColumn(name = "creator_id", nullable = false)
    var user: User? = null

    @NotNull @Column(name = "reason", nullable = false) var reason: Int? = null

    @Column(name = "detail", columnDefinition = "TEXT") var detail: String? = null
}
