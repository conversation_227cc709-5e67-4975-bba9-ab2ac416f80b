package jp.co.torihada.fanme.endpoints

import io.quarkus.security.identity.SecurityIdentity
import io.vertx.ext.web.RoutingContext
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.NewCookie
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.exception.ResourceNotFoundException

@ApplicationScoped
class Util {

    @Inject lateinit var config: Config

    fun isOwnerAccess(uid: String, securityIdentity: SecurityIdentity): Boolean {
        val loginUserUid =
            securityIdentity.attributes.get("login_user_uid") as? String ?: return false
        return uid == loginUserUid
    }

    fun deleteCookie(name: String): NewCookie {
        return NewCookie.Builder(name).value("").path("/").maxAge(0).secure(false).build()
    }

    fun getCurrentUserUid(securityIdentity: SecurityIdentity): String? {
        return securityIdentity.attributes.get("login_user_uid") as? String
    }

    fun getCreatorUid(requestContext: ContainerRequestContext): String {
        return requestContext.getProperty("creator_uid") as? String
            ?: throw ResourceNotFoundException("Creator")
    }

    // shop api basic auth
    @Suppress("MagicNumber")
    fun doBasicAuth(ctx: RoutingContext): Boolean {
        val auth = ctx.request().getHeader("Authorization")
        if (auth == null || !auth.startsWith("Basic ")) {
            return false
        }
        val decoded = String(java.util.Base64.getDecoder().decode(auth.substring(6)))
        val parts = decoded.split(":", limit = 2)
        if (parts.size != 2) {
            return false
        }
        val (username, password) = parts
        return username == config.shopApiName() && password == config.shopApiPass()
    }
}
