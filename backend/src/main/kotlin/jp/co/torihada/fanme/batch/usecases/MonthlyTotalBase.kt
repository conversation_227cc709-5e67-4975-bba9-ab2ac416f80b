package jp.co.torihada.fanme.batch.usecases

import jakarta.enterprise.context.ApplicationScoped
import java.time.LocalDateTime

@ApplicationScoped
class MonthlyTotalBase {
    data class StartEndDateTimes(val startDateTime: LocalDateTime, val endDateTime: LocalDateTime)

    fun toUtcMonthPeriod(yearMonth: String): StartEndDateTimes {
        val formattedYearMonth = StringBuilder(yearMonth).insert(4, "-").toString()
        val startDateTimeJst =
            try {
                LocalDateTime.parse("$formattedYearMonth-01T00:00:00")
            } catch (e: Exception) {
                throw IllegalArgumentException("Invalid yearMonth: $yearMonth")
            }

        // １ヶ月後の最終日時を求める
        val endDateTimeJst = startDateTimeJst.plusMonths(1).minusSeconds(1)

        return StartEndDateTimes(startDateTimeJst, endDateTimeJst)
    }
}
