package jp.co.torihada.fanme.modules.shop.usecases.digitalGacha

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ItemTypeIsNotDigitalGachaException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.models.GachaReceivedFile
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem

@ApplicationScoped
class GetPullableGachaCount {
    data class Input(val itemId: Long, val purchaserUserId: String)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class Output(val itemId: Long, val remainingPullCount: Int)

    fun execute(params: Input): Result<Output, FanmeException> {
        val item = Item.findById(params.itemId) ?: return Err(ResourceNotFoundException("Item"))

        // ItemTypeがDIGITAL_GACHAの時のみ処理を行う
        if (item.itemType != ItemType.DIGITAL_GACHA) {
            return Err(ItemTypeIsNotDigitalGachaException())
        }

        val purchasedItems =
            PurchasedItem.findByItemIdAndPurchaserUid(
                itemId = params.itemId,
                purchaserUid = params.purchaserUserId,
            )

        // 購入したアイテムが存在しない場合は、残りの引き換え回数は0
        if (purchasedItems.isEmpty()) {
            return Ok(Output(itemId = params.itemId, remainingPullCount = 0))
        }
        val purchasedTotalQuantity = purchasedItems.sumOf { it.quantity }
        val receivedFiles =
            GachaReceivedFile.findByPurchaserUidAndItemId(
                purchaserUid = params.purchaserUserId,
                itemId = params.itemId,
            )

        val alreadyPullCount = receivedFiles.size
        val pullCount = purchasedTotalQuantity - alreadyPullCount

        if (pullCount < 0) {
            return Ok(Output(itemId = params.itemId, remainingPullCount = 0))
        }

        return Ok(Output(itemId = params.itemId, remainingPullCount = pullCount))
    }
}
