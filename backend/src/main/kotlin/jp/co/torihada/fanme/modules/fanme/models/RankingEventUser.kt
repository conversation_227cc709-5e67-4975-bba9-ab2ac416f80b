package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "ranking_event_users")
class RankingEventUser : BaseModel() {

    @ManyToOne
    @JoinColumn(name = "ranking_event_creator_id", nullable = false)
    var rankingEventCreator: RankingEventCreator? = null

    @NotNull @ManyToOne @JoinColumn(name = "user_id", nullable = false) var user: User? = null

    @Column(name = "rank") var rank: Int? = null

    @Column(name = "yell_count") var yellCount: Int? = null

    @Column(name = "aggregation_at")
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var aggregationAt: Instant? = null

    @Column(name = "previous_rank") var previous_rank: Int? = null

    @Column(name = "previous_yell_count") var previousYellCount: Int? = null

    @Column(name = "previous_aggregation_at")
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var previousAggregationAt: Instant? = null
}
