package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "creator_tokens")
class UserToken : BaseModel() {
    @OneToOne @JoinColumn(name = "creator_id", nullable = false) var user: User? = null

    @Column(name = "id_token", columnDefinition = "TEXT") var idToken: String? = null

    companion object : PanacheCompanion<UserToken> {
        fun findByCreator(user: User): UserToken? {
            return find("user = ?1", user).firstResult()
        }

        fun findByIdToken(idToken: String): UserToken? {
            return find("idToken = ?1", idToken).firstResult()
        }
    }
}
