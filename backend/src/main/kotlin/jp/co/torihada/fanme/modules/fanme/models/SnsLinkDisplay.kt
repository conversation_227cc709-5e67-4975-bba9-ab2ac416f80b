package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "sns_link_displays")
class SnsLinkDisplay : BaseModel() {
    @NotNull
    @ManyToOne(optional = false)
    @JoinColumn(name = "profile_id", nullable = false)
    var profile: Profile? = null

    @NotNull
    @OneToOne(optional = false)
    @JoinColumn(name = "sns_link_id", nullable = false)
    var snsLink: SnsLink? = null

    @NotNull
    @Column(name = "display_order_number", nullable = false)
    var displayOrderNumber: Int? = null

    @NotNull @Column(name = "displayable", nullable = false) var displayable: Boolean? = false
}
