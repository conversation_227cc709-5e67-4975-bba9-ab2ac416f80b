package jp.co.torihada.fanme.endpoints.shop

import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.endpoints.BaseCreatorAccountIdentityEndpoint
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.modules.shop.controllers.SellerAppsController

@Path("/shops/{creator_account_identity}/seller-apps")
class SellerAppsEndpoint : BaseCreatorAccountIdentityEndpoint() {
    @Inject lateinit var requestContext: ContainerRequestContext
    @Inject lateinit var handler: SellerAppsController
    @Inject lateinit var util: Util

    @GET
    @Path("/digital-fan-letter-link")
    @Produces(MediaType.APPLICATION_JSON)
    fun getDigitalFanLetterLink(): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val result = handler.getDigitalFanLetterLink(creatorUid)
            val entity = ResponseEntity(result, "digital_fan_letter_link")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
