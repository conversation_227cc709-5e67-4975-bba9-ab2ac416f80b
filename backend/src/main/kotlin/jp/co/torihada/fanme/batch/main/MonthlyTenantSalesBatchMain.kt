package jp.co.torihada.fanme.batch.main

import io.quarkus.runtime.QuarkusApplication
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.batch.usecases.MonthlyTenantSalesBatch
import org.jboss.logging.Logger

class MonthlyTenantSalesBatchMain : QuarkusApplication {
    private val logger: Logger = Logger.getLogger(MonthlyTenantSalesBatchMain::class.java)

    @Inject private lateinit var monthlyTenantSalesBatch: MonthlyTenantSalesBatch

    @Transactional
    override fun run(vararg args: String?): Int {
        logger.info("MonthlyTenantSalesBatchMain start")

        val yearMonth =
            if (args.size > 1 && args[1] != null) {
                args[1]
            } else {
                val previousDate =
                    ZonedDateTime.now(ZoneId.of("Asia/Tokyo")).minusDays(1).toLocalDateTime()

                DateTimeFormatter.ofPattern("yyyyMM").format(previousDate)
            }

        try {
            monthlyTenantSalesBatch.execute(yearMonth!!)
            logger.info("MonthlyTenantSalesBatchMain success yearMonth: $yearMonth")
            return 0
        } catch (e: Exception) {
            logger.error("MonthlyTenantSalesBatchMain error yearMonth: $yearMonth", e)
            return 1
        }
    }
}
