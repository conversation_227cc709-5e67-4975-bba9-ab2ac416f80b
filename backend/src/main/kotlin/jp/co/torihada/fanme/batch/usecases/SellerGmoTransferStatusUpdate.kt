package jp.co.torihada.fanme.batch.usecases

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.externals.entity.transfer.DepositSearch
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale
import jp.co.torihada.fanme.modules.payment.models.SellerAccountActivity
import jp.co.torihada.fanme.modules.payment.models.SellerAccountBalance
import jp.co.torihada.fanme.modules.payment.models.SellerGmoTransfer
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import org.jboss.resteasy.reactive.ClientWebApplicationException

@ApplicationScoped
class SellerGmoTransferStatusUpdate {
    private val logger: Logger = Logger.getLogger(SellerGmoTransferStatusUpdate::class.java)

    @Inject private lateinit var commonConfig: CommonConfig
    @Inject private lateinit var paymentConfig: PaymentConfig

    @Inject @RestClient private lateinit var extGmoTransferClient: ExtGmoTransferClient

    fun execute() {
        logger.info("start SellerGmoTransferStatusUpdate")

        val sellerGmoTransfer =
            SellerGmoTransfer.findByStatuses(
                listOf(
                    Const.GmoTransferStatus.TransferNotRegistered.value,
                    Const.GmoTransferStatus.TransferRegistered.value,
                    Const.GmoTransferStatus.TransferDataRegistered.value,
                )
            )

        val now = ZonedDateTime.now(ZoneOffset.UTC) // 現在時刻をUTCで取得
        val linkLimitDateTime = now.minus(5, ChronoUnit.MINUTES).toInstant()

        sellerGmoTransfer.forEach { sellerGmoTransfer ->
            if (sellerGmoTransfer.depositId == null) {
                return@forEach
            }

            // TODO ここでエラー処理を追加する
            // 取得できなかった場合は申請が心配している可能性があるのでリンクの有効期限を確認してlinkLimitDateTimeを過ぎていた場合はstatusを変更する
            var response: DepositSearch.Response? = null
            try {
                response =
                    extGmoTransferClient.depositSearch(
                        DepositSearch.Request(
                            shopId = paymentConfig.gmoTransferShopId(),
                            shopPass = paymentConfig.gmoTransferShopPass(),
                            depositId = sellerGmoTransfer.depositId!!,
                        )
                    )
            } catch (e: ClientWebApplicationException) {
                val errorResponseBody = e.response.readEntity(String::class.java)
                val objectMapper = ObjectMapper().registerKotlinModule()
                val errorResponses: List<DepositSearch.ErrorResponse> =
                    objectMapper.readValue(errorResponseBody)
                val isErrorInfoMatch = errorResponses.any { it.errInfo == "BA1540002" }

                if (
                    sellerGmoTransfer.status == Const.GmoTransferStatus.TransferRegistered.value &&
                        sellerGmoTransfer.createdAt!! < linkLimitDateTime &&
                        isErrorInfoMatch
                ) {
                    sellerGmoTransfer.status = Const.GmoTransferStatus.TransferUrlExpired.value
                } else {
                    logger.error("depositSearch error. depositId: ${sellerGmoTransfer.depositId}")
                    errorResponses.forEach { errorResponse ->
                        val errorCode = errorResponse.errCode
                        val errorInfo = errorResponse.errInfo
                        logger.error(
                            "Failed to depositSearch: ErrorCode: $errorCode, ErrorInfo: $errorInfo"
                        )
                    }
                }

                return@forEach
            } catch (e: Exception) {
                logger.error("depositSearch error. depositId: ${sellerGmoTransfer.depositId}")
                return@forEach
            }

            val gmoTransferStatus =
                Const.GmoTransferStatus.fromValue(response.bank?.result.orEmpty())
            val (accountActivityCode, transferFee, gmoTransferFee) =
                when (gmoTransferStatus) {
                    Const.GmoTransferStatus.TransferCompleted -> {
                        Triple(
                            Const.AccountActivityCode.TransferSuccess.value,
                            440,
                            if (sellerGmoTransfer.amount >= 30000) 400 else 300,
                        )
                    }

                    in listOf(
                        Const.GmoTransferStatus.TransferDataFailed,
                        Const.GmoTransferStatus.TransferFailed,
                    ) -> {
                        Triple(Const.AccountActivityCode.TransferFailed.value, 220, 200)
                    }

                    else -> {
                        sellerGmoTransfer.status = response.bank?.result.orEmpty()
                        sellerGmoTransfer.detailCode = response.bank?.resultDetail.orEmpty()

                        return@forEach
                    }
                }

            // 送金情報の更新
            sellerGmoTransfer.status = response.bank?.result.orEmpty()
            sellerGmoTransfer.detailCode = response.bank?.resultDetail.orEmpty()
            sellerGmoTransfer.fee = transferFee
            sellerGmoTransfer.gmoFee = gmoTransferFee

            // クリエイターの引き出し金額の更新
            val sellerAccountBalance =
                SellerAccountBalance.findBySellerUserId(sellerGmoTransfer.sellerUserId!!)
                    ?: run {
                        logger.error(
                            "sellerAccountBalance not found. sellerUserId: ${sellerGmoTransfer.sellerUserId}"
                        )
                        return@forEach
                    }

            // クリエイターの現在の残高
            val currentAmount = sellerAccountBalance.amount

            if (accountActivityCode == Const.AccountActivityCode.TransferSuccess.value) {
                // クリエイター売上金出金データ作成
                SellerAccountActivity()
                    .apply {
                        this.tenant = commonConfig.tenant()
                        this.sellerUserId = sellerGmoTransfer.sellerUserId
                        this.amount = sellerGmoTransfer.amount
                        this.activityType = Const.AccountActivityType.Withdrawal.value
                        this.activityCode = Const.AccountActivityCode.AppSales.value
                        this.balance =
                            sellerAccountBalance.amount?.minus(sellerGmoTransfer.amount) ?: 0
                    }
                    .persist()

                // クリエイターの残高を更新
                sellerAccountBalance.amount =
                    sellerAccountBalance.amount.minus(sellerGmoTransfer.amount)
            }

            // クリエイターの送金手数料データ作成(送金失敗時も含む)
            val sellerAccountActivity =
                SellerAccountActivity().apply {
                    this.tenant = commonConfig.tenant()
                    this.sellerUserId = sellerGmoTransfer.sellerUserId
                    this.amount = transferFee
                    this.activityType = Const.AccountActivityType.Withdrawal.value
                    this.activityCode = accountActivityCode
                    this.balance = sellerAccountBalance.amount.minus(transferFee)
                }
            sellerAccountActivity.persist()

            // 送金手数料を減算してクリエイターの残高を更新
            sellerAccountBalance.amount = sellerAccountBalance.amount.minus(transferFee)

            // ステータスが送金済、失効以外の月次データ取得
            val monthlySellerSales =
                MonthlySellerSale.findBySellerAndStatus(
                    sellerGmoTransfer.sellerUserId!!,
                    listOf(
                        Const.MonthlySellerSaleStatus.NotTransferred.value,
                        Const.MonthlySellerSaleStatus.Transferring.value,
                    ),
                )

            // TODO monthlySellerSalesがnullの場合はreturnする

            // monthlySellerSalesの更新
            when (gmoTransferStatus) {
                Const.GmoTransferStatus.TransferCompleted -> {
                    // 月次データの古いものから送金済みに変更する
                    var withdrawalAmount = sellerGmoTransfer.amount + transferFee
                    monthlySellerSales
                        .sortedBy { it.yearMonth }
                        .forEach {
                            if (it.remainingAmount!! <= withdrawalAmount) {
                                withdrawalAmount = withdrawalAmount.minus(it.remainingAmount!!)
                                it.remainingAmount = 0
                                // 送金済みに変更
                                it.transferStatus = Const.MonthlySellerSaleStatus.Transferred.value
                            } else {
                                it.remainingAmount = it.remainingAmount?.minus(withdrawalAmount)
                                withdrawalAmount = 0
                                it.transferSalesAmount = transferFee
                            }
                            if (withdrawalAmount == 0) {
                                return@forEach
                            }
                        }
                }

                else -> {
                    // 送金が失敗した場合
                    val monthSellerSale = monthlySellerSales.sortedBy { it.yearMonth }.firstOrNull()
                    if (monthSellerSale != null) {
                        monthSellerSale.transferSalesAmount = transferFee
                        monthSellerSale.remainingAmount =
                            monthSellerSale.remainingAmount?.minus(sellerGmoTransfer.amount)
                    }
                }
            }
        }
        Thread.sleep(500)

        logger.info("end SellerGmoTransferStatusUpdate")
    }
}
