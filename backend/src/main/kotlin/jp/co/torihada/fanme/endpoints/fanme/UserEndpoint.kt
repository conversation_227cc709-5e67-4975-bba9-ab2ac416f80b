package jp.co.torihada.fanme.endpoints.fanme

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.CookieParam
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/fanme/users")
@Tag(name = "FANME", description = "FANME APIサーバー")
class UserEndpoint {

    @Inject private lateinit var securityIdentity: SecurityIdentity

    @Inject private lateinit var handler: UserController

    @Inject lateinit var util: Util

    @GET
    @Path("/current")
    @RolesAllowed("LoginUser")
    fun getCurrentUser(): Any {
        try {
            val userUuid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            return handler.getUser(userUuid)
        } catch (e: Exception) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
        }
    }

    @GET
    @Path("/{user_uuid}")
    fun getUser(@PathParam("user_uuid") userUuid: String): Any {
        return try {
            handler.getUser(userUuid)
        } catch (e: Exception) {
            Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
        }
    }

    @GET
    @Path("/id_token")
    fun getIdToken(@CookieParam("creator_login_state") userTokenId: Long): Any {
        val res = handler.getIdToken(userTokenId)
        val deleteCookie = util.deleteCookie("creator_login_state")

        return Response.ok(res).cookie(deleteCookie).build()
    }
}
