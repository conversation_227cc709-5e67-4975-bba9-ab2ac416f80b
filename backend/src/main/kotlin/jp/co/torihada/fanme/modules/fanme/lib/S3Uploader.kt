package jp.co.torihada.backend.lib

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.nio.file.Path
import jp.co.torihada.fanme.modules.fanme.Config
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.PutObjectRequest

@ApplicationScoped
class S3Uploader {

    @Inject private lateinit var s3: S3Client

    @Inject private lateinit var config: Config

    fun execute(
        remotePath: String,
        remoteFileName: String,
        contentType: String,
        fileToUpload: Path,
    ): Result<String, Exception> {
        return try {
            val putRequest =
                PutObjectRequest.builder()
                    .bucket(config.s3BucketName())
                    .key("$remotePath/$remoteFileName")
                    .metadata(mapOf("Content-Type" to contentType))
                    .build()
            s3.putObject(putRequest, fileToUpload)

            Ok(remoteFileName)
        } catch (e: Exception) {
            Log.error("Failed to save file to S3", e)
            Err(e)
        }
    }
}
