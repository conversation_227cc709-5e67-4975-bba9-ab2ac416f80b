package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonBackReference
import com.fasterxml.jackson.annotation.JsonManagedReference
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "profiles")
class Profile : BaseModel() {
    @OneToOne
    @JsonBackReference
    @JoinColumn(name = "creator_id", nullable = false)
    var user: User? = null

    @NotNull
    @Column(name = "bio", nullable = false, columnDefinition = "TEXT")
    var bio: String? = null

    @Size(max = 255) @Column(name = "header_image") var headerImage: String? = null

    @Size(max = 255)
    @NotNull
    @Column(name = "sns_link_color", nullable = false)
    var snsLinkColor: String? = null

    @NotNull @Column(name = "official_flg", nullable = false) var officialFlg: Boolean? = false

    @JsonManagedReference
    @OneToOne(mappedBy = "profile", orphanRemoval = true)
    var covers: ProfileCover? = null

    @JsonManagedReference
    @OneToOne(mappedBy = "profile", orphanRemoval = true)
    var themeColor: ProfileThemeColor? = null

    @OneToMany(mappedBy = "profile", orphanRemoval = true)
    var snsLinks: MutableSet<SnsLink> = mutableSetOf()
}
