package jp.co.torihada.fanme.endpoints.shop

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.PurchaseItemResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.shop.controllers.PurchasedItemController
import jp.co.torihada.fanme.odata.OData
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/shops/current/purchased-items")
class PurchasedItemEndpoint {

    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var requestContext: ContainerRequestContext
    @Inject lateinit var handler: PurchasedItemController
    @Inject lateinit var util: Util

    @GET
    @RolesAllowed("LoginUser")
    @Produces("application/json")
    fun getPurchasedItems(@QueryParam("include_tip") includeTip: Boolean?): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val odata = requestContext.getProperty("odata") as OData?
            // 購入済みコンテンツではnullまたはfalse、購入履歴ではtrue
            val result = handler.getPurchasedItems(userUid, includeTip ?: false, odata)
            val entity = ResponseEntity(result, "purchased_items")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @RolesAllowed("LoginUser")
    @Produces("application/json")
    @Path("/{purchased_item_id}")
    @APIResponse(responseCode = "200")
    @APIResponseSchema(PurchaseItemResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getPurchasedItem(@PathParam("purchased_item_id") purchasedItemId: Long): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.getPurchasedItem(userUid, purchasedItemId)
            val entity = ResponseEntity(result, "purchased_item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
