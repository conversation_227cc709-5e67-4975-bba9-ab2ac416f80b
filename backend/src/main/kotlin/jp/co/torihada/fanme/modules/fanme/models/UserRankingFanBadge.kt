package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import jakarta.persistence.*

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "user_ranking_fan_badges")
class UserRankingFanBadge : BaseModel() {
    @ManyToOne(optional = false)
    @JoinColumn(name = "ranking_fan_badge_id", nullable = false)
    var rankingFanBadge: RankingFanBadge? = null

    @ManyToOne(optional = false)
    @JoinColumn(name = "creator_id", nullable = false)
    var creator: User? = null

    @ManyToOne(optional = false)
    @JoinColumn(name = "user_id", nullable = false)
    var user: User? = null
}
