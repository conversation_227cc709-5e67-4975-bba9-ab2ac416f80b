import jakarta.ws.rs.core.NewCookie
import java.security.SecureRandom

abstract class BaseController {
    protected fun generateCookie(name: String, value: String): New<PERSON><PERSON>ie {
        return NewCookie.Builder(name)
            .value(value)
            .path("/")
            .maxAge(24 * 60 * 60)
            .secure(false)
            .build()
    }

    protected fun deleteCookie(name: String): NewCookie {
        return NewCookie.Builder(name).value("").path("/").maxAge(0).secure(false).build()
    }

    protected fun generateRandomHex(length: Int): String {
        val randomBytes = ByteArray(length)
        SecureRandom().nextBytes(randomBytes)
        return randomBytes.joinToString("") { "%02x".format(it) }
    }

    protected fun generateTokenString(): String {
        val random = SecureRandom()
        val bytes = ByteArray(16)
        random.nextBytes(bytes)
        return bytes.joinToString("") { "%02x".format(it) }
    }
}
