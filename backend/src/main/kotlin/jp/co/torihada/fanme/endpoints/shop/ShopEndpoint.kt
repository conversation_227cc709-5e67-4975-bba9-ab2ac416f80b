package jp.co.torihada.fanme.endpoints.shop

import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.ShopResponseBody
import jp.co.torihada.fanme.endpoints.BaseCreatorAccountIdentityEndpoint
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.modules.shop.controllers.ShopController
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/shops/{creator_account_identity}")
class ShopEndpoint : BaseCreatorAccountIdentityEndpoint() {

    @Inject lateinit var requestContext: ContainerRequestContext

    @Inject lateinit var handler: ShopController

    @Inject lateinit var util: Util

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(ShopResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getShop(): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val result = handler.getShop(creatorUid, true)
            val entity = ResponseEntity(result, "shop")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
