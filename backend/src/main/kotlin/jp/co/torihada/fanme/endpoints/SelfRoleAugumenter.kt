package jp.co.torihada.fanme.endpoints

import io.quarkus.security.identity.AuthenticationRequestContext
import io.quarkus.security.identity.SecurityIdentity
import io.quarkus.security.identity.SecurityIdentityAugmentor
import io.quarkus.security.runtime.QuarkusSecurityIdentity
import io.quarkus.vertx.http.runtime.security.HttpSecurityUtils
import io.smallrye.jwt.auth.principal.DefaultJWTCallerPrincipal
import io.smallrye.mutiny.Uni
import io.vertx.ext.web.RoutingContext
import jakarta.enterprise.context.ApplicationScoped
import jakarta.enterprise.context.Dependent
import jakarta.enterprise.context.control.ActivateRequestContext
import jakarta.inject.Inject
import java.util.function.Supplier
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class UserSecurityIdentityAugmentor : SecurityIdentityAugmentor {
    @Inject private lateinit var identitySupplierInstance: SecurityIdentitySupplier

    @ActivateRequestContext
    override fun augment(
        identity: SecurityIdentity,
        context: AuthenticationRequestContext,
        attributes: Map<String, Any>?,
    ): Uni<SecurityIdentity> {
        if (identity.isAnonymous) {
            return Uni.createFrom().item(identity)
        }

        val routingContext: RoutingContext? =
            HttpSecurityUtils.getRoutingContextAttribute(attributes)

        identitySupplierInstance.apply {
            this.identity = identity
            this.routingContext = routingContext
        }

        return context.runBlocking(identitySupplierInstance)
    }

    override fun augment(
        identity: SecurityIdentity,
        context: AuthenticationRequestContext,
    ): Uni<SecurityIdentity> {
        return augment(identity, context, emptyMap())
    }
}

@Dependent
class SecurityIdentitySupplier : Supplier<SecurityIdentity> {
    var identity: SecurityIdentity? = null
    var routingContext: RoutingContext? = null
    @Inject lateinit var config: Config

    @ActivateRequestContext
    override fun get(): SecurityIdentity {
        val builder = QuarkusSecurityIdentity.builder(identity)
        val principal = identity?.principal as? DefaultJWTCallerPrincipal
        val uid = principal?.subject
        val exp = principal?.expirationTime
        val expiresAt = exp?.minus(System.currentTimeMillis() / 1000)

        if (uid == null || (expiresAt != null && expiresAt < 0)) {
            return builder.build()
        }

        val path = routingContext?.request()?.path()
        if (path != null && path.contains("/console")) {
            val user = User.findByUuid(uid)
            val consoleUser = ConsoleUser.findByUserId(user?.id ?: 0L)
            val userRole = consoleUser?.role
            builder.addRole(userRole)
        }
        builder.addAttribute("login_user_uid", uid)
        builder.addRole("LoginUser")

        return builder.build()
    }
}
