package jp.co.torihada.fanme.endpoints

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.json.JsonMapper
import jakarta.validation.ConstraintViolationException
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.container.ContainerResponseContext
import jakarta.ws.rs.core.Response
import jakarta.ws.rs.ext.Provider
import java.util.*
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import org.jboss.logging.Logger
import org.jboss.resteasy.reactive.server.ServerResponseFilter

@Provider
class ResponseFilter {

    @ServerResponseFilter
    fun baseResponseFilter(
        requestContext: ContainerRequestContext,
        responseContext: ContainerResponseContext,
    ) {
        val entity = responseContext.entity
        val status = responseContext.status
        val path = requestContext.uriInfo.path
        val logger = Logger.getLogger(ResponseFilter::class.java)

        if (status >= 400) {
            if (entity != null) {
                // 定義したエラークラス
                if (entity is FanmeException) {
                    logger.error(entity.message)
                    logger.error(entity.stackTraceToString())
                    if (entity is ResourceNotFoundException) {
                        responseContext.status = Response.Status.NOT_FOUND.statusCode
                    }
                    responseContext.entity = BaseResponseBody(data = {}, errors = entity.errors)
                    return
                }
                // バリデーションエラー
                if (entity is ConstraintViolationException) {
                    logger.error(entity.message)
                    logger.error(entity.stackTraceToString())
                    val errors = mutableListOf<ErrorObject>()
                    entity.constraintViolations.forEach {
                        errors.add(ErrorObject(code = 0, message = it.message))
                    }
                    responseContext.status = Response.Status.BAD_REQUEST.statusCode
                    responseContext.entity = BaseResponseBody(data = {}, errors = errors)
                    return
                }
                if (entity is Exception) {
                    logger.error(entity.message)
                    logger.error(entity.stackTraceToString())
                    val errorObject = ErrorObject(message = "Internal Server Error", code = 0)
                    responseContext.entity =
                        BaseResponseBody(data = {}, errors = listOf(errorObject))
                    return
                }
            }
            logger.error(entity)
            val internalServerError =
                listOf(ErrorObject(message = "Internal Server Error", code = 0))
            responseContext.entity = BaseResponseBody(data = {}, errors = internalServerError)
            return
        } else {
            if (path == "/hc") return // ヘルスチェックの場合は何もしない
            if (entity == null) return // 返すリソースがない場合は何もしない

            try {
                val mapper: ObjectMapper = JsonMapper.builder().findAndAddModules().build()
                var topLevelPropertyName = "data"
                var jsonBody = ""
                if (entity is ResponseEntity) {
                    topLevelPropertyName = entity.topLevelPropertyName
                    jsonBody = mapper.writeValueAsString(entity.entity)
                } else {
                    topLevelPropertyName = map2JsonPropertyName(entity)
                    jsonBody = mapper.writeValueAsString(entity)
                }
                val entityJsonString = "{\"${topLevelPropertyName}\": ${jsonBody}}"
                responseContext.entity =
                    BaseResponseBody(
                        data = ObjectMapper().readTree(entityJsonString),
                        errors = listOf(),
                    )
            } catch (e: Exception) {
                logger.error(e.message)
                logger.error(e.stackTraceToString())
                val internalServerError =
                    listOf(ErrorObject(message = "Internal Server Error", code = 0))
                responseContext.entity = BaseResponseBody(data = {}, errors = internalServerError)
            }
        }
    }

    private val classMapper =
        mapOf(
            "CartItem" to "cart_item",
            "ItemPasswordUnlockCache" to "item_password_unlock_cache",
            "OrderResult" to "order_result",
            "ApplePayToken" to "apple_pay_token",
            "TipUpperLimit" to "tip_limit",
        )

    private fun map2JsonPropertyName(entity: Any): String {
        val classNameSplitPeriod = entity.javaClass.name.split(".")
        val classNameSplitDollar = classNameSplitPeriod[classNameSplitPeriod.size - 1].split("$")
        val className = classNameSplitDollar[classNameSplitDollar.size - 1]

        return classMapper[className] ?: className.lowercase(Locale.getDefault())
    }
}
