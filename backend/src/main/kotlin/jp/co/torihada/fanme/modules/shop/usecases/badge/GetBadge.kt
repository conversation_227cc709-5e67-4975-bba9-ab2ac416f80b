package jp.co.torihada.fanme.modules.shop.usecases.badge

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.models.BadgeType
import jp.co.torihada.fanme.modules.shop.models.UserBadge

@ApplicationScoped
class GetBadge {
    data class Input(val userUid: String, val itemId: Long, val badgeType: BadgeType)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class Output(val itemId: Long, val isAcquired: Boolean, val rank: Int?)

    fun execute(params: Input): Result<Output, FanmeException> {

        // バッジの取得
        val badge =
            UserBadge.findByItemIdAndUserUidAndBadgeType(
                itemId = params.itemId,
                userUid = params.userUid,
                badgeType = params.badgeType,
            )
        val isAcquired = badge != null
        var rank: Int? = null
        if (isAcquired) {
            // ランキングを取得
            val userBadgeWithRank =
                UserBadge.findTop100ByCreatedAt(
                    itemId = params.itemId,
                    badgeType = BadgeType.DIGITAL_GACHA_COMPLETE,
                )
            if (userBadgeWithRank.isNotEmpty()) {
                // ランキングを取得
                userBadgeWithRank
                    .indexOfFirst { it.userUid == params.userUid }
                    .takeIf { it >= 0 }
                    ?.let { rank = it + 1 }
            } else {
                rank = null
            }
        }

        return Ok(Output(itemId = params.itemId, isAcquired = isAcquired, rank = rank))
    }
}
