package jp.co.torihada.fanme.endpoints.shop

import io.quarkus.security.identity.SecurityIdentity
import io.vertx.ext.web.RoutingContext
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.Context
import jakarta.ws.rs.core.HttpHeaders
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.TipLimitResponseBody
import jp.co.torihada.fanme.endpoints.BaseCreatorAccountIdentityEndpoint
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.endpoints.shop.request.order.CreateOrderRequest
import jp.co.torihada.fanme.endpoints.shop.request.order.FinalizeCreditCard3DSecureRequest
import jp.co.torihada.fanme.endpoints.shop.request.order.RequestConverter
import jp.co.torihada.fanme.endpoints.shop.request.order.UpdateOrderRequest
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.shop.controllers.OrderController
import jp.co.torihada.fanme.odata.OData
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/orders")
class OrderEndpoint : BaseCreatorAccountIdentityEndpoint() {
    val logger: org.jboss.logging.Logger =
        org.jboss.logging.Logger.getLogger(OrderEndpoint::class.java)

    @Inject lateinit var securityIdentity: SecurityIdentity

    @Inject lateinit var requestContext: ContainerRequestContext

    @Inject lateinit var handler: OrderController

    @Inject lateinit var requestConverter: RequestConverter

    @Inject lateinit var util: Util

    @GET
    @RolesAllowed("LoginUser")
    fun getOrders(): Response {
        val odata = requestContext.getProperty("odata") as OData
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.getOrders(userUid, odata)
            val entity = ResponseEntity(result, "orders")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @POST
    @RolesAllowed("LoginUser")
    @Consumes("application/json")
    @Produces("application/json")
    fun createOrder(@Context headers: HttpHeaders, requestBody: CreateOrderRequest): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val fanmeToken =
                headers
                    .getHeaderString("Authorization")
                    .takeIf { it.startsWith("Bearer ") }
                    ?.removePrefix("Bearer ")
            val request = requestConverter.requestToCreateOrder(userUid, requestBody)
            val result = handler.createOrder(request, fanmeToken)
            val entity = ResponseEntity(result, "order")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @PUT
    @Consumes("application/json")
    @Produces("application/json")
    fun updateOrder(requestBody: UpdateOrderRequest, @Context ctx: RoutingContext): Response {
        return try {
            if (!util.doBasicAuth(ctx)) {
                return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(UnAuthorizedException())
                    .build()
            }
            handler.updateOrder(
                requestBody.transactionId,
                requestBody.checkoutId,
                requestBody.status,
            )
            Response.ok().build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @POST
    @Path("/finalize-credit-card-3d-secure")
    @Consumes("application/json")
    @Produces("application/json")
    fun finalizeCreditCard3DSecure(
        requestBody: FinalizeCreditCard3DSecureRequest,
        @Context ctx: RoutingContext,
    ): Response {
        return try {
            if (!util.doBasicAuth(ctx)) {
                return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(UnAuthorizedException())
                    .build()
            }
            handler.finalizeCreditCard3DSecure(requestBody.transactionId, requestBody.checkoutId)
            Response.ok().build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/tip-upper-limit/{creator_account_identity}")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(TipLimitResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getTipLimit(): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.getTipLimit(creatorUid, userUid)
            val entity = ResponseEntity(result, "tip_limit")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/convenience-fees")
    @Produces("application/json")
    fun getConvenienceFees(): Response {
        return try {
            val result = handler.getConvenienceFees()
            val entity = ResponseEntity(result, "convenience_fees")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
