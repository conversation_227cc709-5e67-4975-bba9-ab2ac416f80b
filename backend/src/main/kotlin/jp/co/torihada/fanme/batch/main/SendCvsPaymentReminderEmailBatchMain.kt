package jp.co.torihada.fanme.batch.main

import io.quarkus.runtime.QuarkusApplication
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.batch.usecases.SendCvsPaymentReminderEmailBatch
import org.jboss.logging.Logger

class SendCvsPaymentReminderEmailBatchMain : QuarkusApplication {
    private val logger: Logger = Logger.getLogger(SendCvsPaymentReminderEmailBatchMain::class.java)
    @Inject private lateinit var sendCvsPaymentReminderEmailBatch: SendCvsPaymentReminderEmailBatch

    @Transactional
    override fun run(vararg args: String?): Int {
        logger.info("SendCvsPaymentReminderEmailBatchMain start")
        try {
            sendCvsPaymentReminderEmailBatch.execute()
            logger.info("SendCvsPaymentReminderEmailBatchMain success")
            return 0
        } catch (e: Exception) {
            logger.error("SendCvsPaymentReminderEmailBatchMain error", e)
            return 1
        }
    }
}
