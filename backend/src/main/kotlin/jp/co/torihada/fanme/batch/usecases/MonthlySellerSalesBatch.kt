package jp.co.torihada.fanme.batch.usecases

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale
import jp.co.torihada.fanme.modules.payment.models.Transaction
import org.jboss.logging.Logger

@ApplicationScoped
class MonthlySellerSalesBatch {
    private val logger: Logger = Logger.getLogger(SellerSalesExpire::class.java)

    @Inject private lateinit var config: Config
    @Inject private lateinit var monthlyTotalBase: MonthlyTotalBase

    // 世界標準時間を入力
    fun execute(yearMonth: String) {
        logger.info("MonthlySellerSalesBatch start yearMonth: $yearMonth")

        // バッチで処理する期間の設定
        val startEndDateTimes = monthlyTotalBase.toUtcMonthPeriod(yearMonth)

        // クリエイターの売上情報取得
        val monthlyAmounts =
            Transaction.findMonthlySellerAmounts(
                tenant = config.tenant(),
                status = Const.TransactionStatus.Success.value,
                startDateTime = startEndDateTimes.startDateTime.toInstant(ZoneOffset.UTC),
                endDateTime = startEndDateTimes.endDateTime.toInstant(ZoneOffset.UTC),
            )

        // 売上送金失効期限(150日後)
        val formattedYearMonth = StringBuilder(yearMonth).insert(4, "-").toString()
        val expirationDate =
            LocalDateTime.parse("${formattedYearMonth}-01T00:00:00")
                .plusMonths(1)
                .plusDays(150 - 1)
                .atZone(ZoneId.of("Asia/Tokyo"))
                .toInstant()

        val thisMonth =
            DateTimeFormatter.ofPattern("yyyyMM").format(LocalDateTime.now(ZoneId.of("Asia/Tokyo")))
        val approved = yearMonth < thisMonth

        // 月次売上情報の該当月のデータを削除
        MonthlySellerSale.deleteByTenantAndYearMonth(config.tenant(), yearMonth)

        // 月次売上情報の登録
        monthlyAmounts.forEach {
            // TODO ログ追加

            val monthlySellerSale =
                MonthlySellerSale().apply {
                    this.tenant = config.tenant()
                    this.sellerUserId = it.sellerUserId
                    this.yearMonth = yearMonth
                    this.transactionAmount = it.transactionAmount
                    this.miniappSalesAmount = 0
                    this.sellerSalesAmount = it.sellerSaleAmount
                    this.developerSalesAmount = 0
                    this.remainingAmount = it.sellerSaleAmount
                    this.transactionAmount = it.transactionAmount
                    this.expirationDate = expirationDate
                    this.transferStatus = Const.MonthlySellerSaleStatus.NotTransferred.value
                    this.approved = approved
                    this.merged = false
                }

            // TODO ログ追加

            monthlySellerSale.persist()
        }

        logger.info("MonthlySellerSalesBatch end yearMonth: $yearMonth")
    }
}
