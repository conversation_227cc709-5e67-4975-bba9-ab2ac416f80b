package jp.co.torihada.fanme.endpoints.shop

import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.shop.controllers.ShopController
import jp.co.torihada.fanme.modules.shop.controllers.requests.ShopRequest
import org.eclipse.microprofile.openapi.annotations.Operation

@Path("/shops/current")
class CurrentUserShopEndpoint {

    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var handler: ShopController
    @Inject lateinit var util: Util

    @GET
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(
        operationId = "getCurrentUserShop"
    ) // MEMO: shops/{creator_account_identity}:とメソッド名が重複しているため、operationIdを指定している。
    fun getShop(): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.getShop(userUid, false)
            val entity = ResponseEntity(result, "shop")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class CreateShopRequest(
        @JsonProperty("name") val name: String,
        @JsonProperty("description") val description: String?,
        @JsonProperty("header_image_uri") val headerImageUri: String?,
        @JsonProperty("message") val message: String?,
    )

    @POST
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun createShop(requestBody: CreateShopRequest): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val request =
                ShopRequest.CreateShop(
                    creatorUid = userUid,
                    name = requestBody.name,
                    description = requestBody.description,
                    headerImageUri = requestBody.headerImageUri,
                    message = requestBody.message,
                )
            val result = handler.createShop(request)
            val entity = ResponseEntity(result, "shop")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class UpdateShopRequest(
        @JsonProperty("name") val name: String,
        @JsonProperty("description") val description: String?,
        @JsonProperty("header_image_uri") val headerImageUri: String?,
        @JsonProperty("message") val message: String?,
    )

    @PUT
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun updateShop(requestBody: UpdateShopRequest): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val request =
                ShopRequest.UpdateShop(
                    creatorUid = userUid,
                    name = requestBody.name,
                    description = requestBody.description,
                    headerImageUri = requestBody.headerImageUri,
                    message = requestBody.message,
                )
            val result = handler.updateShop(request)
            val entity = ResponseEntity(result, "shop")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
