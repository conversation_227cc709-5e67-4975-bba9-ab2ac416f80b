package jp.co.torihada.fanme.endpoints.fanme

import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.ws.rs.CookieParam
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.QueryParam
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.modules.fanme.Config
import jp.co.torihada.fanme.modules.fanme.controllers.OAuthController
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/auth")
@Tag(name = "FANME", description = "FANME APIサーバー")
class OAuthEndpoint {

    @Inject lateinit var config: Config

    @Inject lateinit var util: Util

    @Inject lateinit var handler: OAuthController

    @GET
    @Path("/fanme")
    @Operation(
        summary = "Fanme OAuth認証開始",
        description = "OAuth認証を開始し、認証プロバイダへのリダイレクト先URLを取得するエンドポイント",
    )
    @APIResponse(responseCode = "200", description = "認証プロバイダへのリダイレクトURLを返す")
    fun fanme(
        @Parameter(description = "戻り先URL") @QueryParam("return_url") returnUrl: String?,
        @Parameter(description = "処理種別") @QueryParam("proc") proc: String?,
    ): Response {
        return handler.fanme(returnUrl, proc)
    }

    @Transactional
    @GET
    @Path("/fanme/callback")
    @Operation(summary = "OAuthコールバック処理", description = "OAuthプロバイダからのコールバックを受け取り、認証処理を実行する")
    @APIResponse(responseCode = "200", description = "認証成功時のレスポンスを返す")
    @APIResponse(responseCode = "500", description = "内部サーバエラーが発生した場合のレスポンスを返す")
    fun callback(
        @Parameter(description = "認証コード") @QueryParam("code") code: String,
        @Parameter(description = "状態値") @QueryParam("state") state: String,
        @Parameter(description = "戻り先URL") @QueryParam("return_url") returnUrl: String,
        @Parameter(description = "Cookie内の状態値") @CookieParam("state") stateCookie: String?,
        @Parameter(description = "Cookie内のNonce") @CookieParam("nonce") nonceCookie: String?,
        @Parameter(description = "ログイン失敗回数")
        @CookieParam("login_failure_count")
        loginFailureCount: String?,
    ): Response {
        try {
            return handler.callback(
                code,
                state,
                returnUrl,
                stateCookie,
                nonceCookie,
                loginFailureCount,
            )
        } catch (e: Exception) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
        }
    }
}
