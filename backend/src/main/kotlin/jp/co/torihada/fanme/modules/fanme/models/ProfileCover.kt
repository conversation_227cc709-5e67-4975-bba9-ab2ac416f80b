package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonBackReference
import com.fasterxml.jackson.annotation.JsonManagedReference
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "profile_covers")
class ProfileCover : BaseModel() {
    @OneToOne(optional = false)
    @JsonBackReference
    @JoinColumn(name = "profile_id", nullable = false)
    var profile: Profile? = null

    @Size(max = 255)
    @NotNull
    @Column(name = "brightness", nullable = false)
    var brightness: String? = null

    @NotNull
    @Column(name = "cover_visibility", nullable = false)
    var coverVisibility: Boolean? = false

    @JsonManagedReference
    @OneToMany(mappedBy = "profileCover", orphanRemoval = true)
    var coverImage: MutableSet<ProfileCoverImage> = mutableSetOf()

    @JsonManagedReference
    @OneToOne(mappedBy = "profileCover", orphanRemoval = true)
    var displayableCoverImage: DisplayableCoverImage? = null
}
