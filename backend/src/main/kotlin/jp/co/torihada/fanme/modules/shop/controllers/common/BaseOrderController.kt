package jp.co.torihada.fanme.modules.shop.controllers.common

import jakarta.inject.Inject
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.controllers.RankingEventController
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.payment.controllers.*
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.shop.Config
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.Util.OrderAmounts
import jp.co.torihada.fanme.modules.shop.Util.PaymentMethod
import jp.co.torihada.fanme.modules.shop.controllers.OrderController.ExecuteOrderResult
import jp.co.torihada.fanme.modules.shop.controllers.requests.OrderRequest
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.Shop
import jp.co.torihada.fanme.modules.shop.services.EmailService
import org.jboss.logging.Logger

abstract class BaseOrderController {
    data class ReturnUrls(val successUrl: String, val errorUrl: String)

    @Inject private lateinit var logger: Logger

    @Inject lateinit var mailer: EmailService

    @Inject private lateinit var shopConfig: Config

    @Inject private lateinit var payment: PaymentController

    @Inject private lateinit var creditCard: CreditCardPaymentController

    @Inject private lateinit var creditCard3DSecure: CreditCard3DSecurePaymentController

    @Inject private lateinit var googlePay: GooglePaymentController

    @Inject private lateinit var applePay: ApplePaymentController

    @Inject private lateinit var convenience: ConveniencePaymentController

    @Inject private lateinit var payPay: PayPayPaymentController

    @Inject private lateinit var rankingEventController: RankingEventController

    private fun buildReturnUrls(
        creatorUid: String,
        itemId: Long? = null,
        isSingleOrder: Boolean = false,
    ): ReturnUrls {
        val creatorAccountIdentity =
            User.findByUuid(creatorUid)?.accountIdentity ?: throw ResourceNotFoundException("User")

        val baseUrl = "${shopConfig.shopFrontUrl()}/@${creatorAccountIdentity}"
        val successPath = if (isSingleOrder) "/item/${itemId}/result/gacha" else "/order/success"

        return ReturnUrls(
            successUrl = baseUrl + successPath,
            errorUrl = "$baseUrl/order?status=error",
        )
    }

    fun createCheckout(
        shop: Shop,
        amounts: OrderAmounts,
        fanmeToken: String? = null,
        userId: String,
        tip: Int,
        paymentMethod: PaymentMethod,
        convenienceParam: OrderRequest.ConvenienceParam?,
        isSingleOrder: Boolean = false,
        itemId: Long? = null,
    ): Checkout {
        val returnUrls = buildReturnUrls(shop.creatorUid!!, itemId, isSingleOrder)
        return payment.createCheckout(
            sellerUserId = shop.creatorUid!!,
            purchaserUserId = userId,
            totalAmount = amounts.total,
            profit = amounts.profit,
            fee = amounts.fee,
            itemsAmount = amounts.itemAmount,
            tipAmount = tip,
            paymentType = PaymentMethod.convertToPaymentType(paymentMethod),
            convenience =
                if (convenienceParam?.convenience != null)
                    Util.getConveniences(convenienceParam.convenience)
                else null,
            deliveryFee = amounts.deliveryFee,
            fanmeToken = fanmeToken,
            successUrl = returnUrls.successUrl,
            errorUrl = returnUrls.errorUrl,
        )
    }

    // paymentでTransactionalを使っているため、ここではTransactionalを使わない
    // 各支払い方法のentryTransactionとexecTransactionを実行する
    fun executeOrder(
        sellerUserUid: String,
        purchaserUserUid: String,
        paymentMethod: PaymentMethod,
        checkout: Checkout,
        amounts: OrderAmounts,
        cardParam: OrderRequest.CardParam?,
        convenienceParam: OrderRequest.ConvenienceParam?,
        googlePayParam: OrderRequest.GooglePayParam?,
        applePayParam: OrderRequest.ApplePayParam?,
        isSingleOrder: Boolean = false,
        itemId: Long? = null,
    ): ExecuteOrderResult {
        if (paymentMethod == PaymentMethod.CONVENIENCE) {
            convenience.entryTransaction(
                checkoutId = checkout.id!!,
                totalAmount = checkout.total!!,
                orderId = checkout.orderId!!,
            )
            val convenienceRequestResult =
                convenience.execTransaction(
                    checkoutId = checkout.id!!,
                    orderId = checkout.orderId!!,
                    customerName = convenienceParam!!.customerName,
                    customerKana = convenienceParam.customerKana,
                    telNo = convenienceParam.telNo,
                )
            return ExecuteOrderResult(
                convenienceCheckout = convenienceRequestResult,
                status = Const.CheckoutStatus.REQSUCCESS,
            )
        } else if (paymentMethod == PaymentMethod.PAY_PAY) {
            // PayPayの場合 returnUrlが必要
            val returnUrls = buildReturnUrls(sellerUserUid, itemId, isSingleOrder)
            val returnUrl =
                shopConfig.shopPaymentUrl() +
                    "/callback/paypay?errorUrl=${returnUrls.errorUrl}&successUrl=${returnUrls.successUrl}"

            payPay.entryTransaction(
                checkoutId = checkout.id!!,
                totalAmount = amounts.total,
                orderId = checkout.orderId!!,
            )

            val result =
                payPay.execTransaction(
                    checkoutId = checkout.id!!,
                    orderId = checkout.orderId!!,
                    returnUrl = returnUrl,
                )

            return ExecuteOrderResult(
                redirectUrl = result.url,
                status = Const.CheckoutStatus.REQSUCCESS,
            )
        } else {
            val result =
                when (paymentMethod) {
                    PaymentMethod.CREDIT_CARD -> {
                        creditCard3DSecure.entryTransaction(
                            checkoutId = checkout.id!!,
                            totalAmount = checkout.total!!,
                            orderId = checkout.orderId!!,
                        )
                        val returnUrl =
                            if (isSingleOrder) {
                                shopConfig.shopPaymentUrl() +
                                    "/callback/single-order/credit-card-3d-secure"
                            } else {
                                shopConfig.shopPaymentUrl() + "/callback/credit-card-3d-secure"
                            }

                        val creditCard3DSecureRequestResult =
                            creditCard3DSecure.execTransaction(
                                checkoutId = checkout.id!!,
                                orderId = checkout.orderId!!,
                                sellerUserUid = sellerUserUid,
                                purchaserUserUid = purchaserUserUid,
                                cardSequence = cardParam!!.cardSequence,
                                returnUrl = returnUrl,
                            )
                        return ExecuteOrderResult(
                            status = Const.CheckoutStatus.REQSUCCESS,
                            redirectUrl = creditCard3DSecureRequestResult.redirectUrl,
                        )
                    }

                    PaymentMethod.GOOGLE_PAY -> {
                        googlePay.entryTransaction(
                            checkoutId = checkout.id!!,
                            totalAmount = amounts.total,
                            orderId = checkout.orderId!!,
                        )
                        googlePay.execCardTransaction(
                            checkoutId = checkout.id!!,
                            orderId = checkout.orderId!!,
                            sellerUserUid = sellerUserUid,
                            purchaserUserUid = purchaserUserUid,
                            token = googlePayParam!!.token,
                        )
                    }

                    PaymentMethod.APPLE_PAY -> {
                        applePay.entryTransaction(
                            checkoutId = checkout.id!!,
                            totalAmount = amounts.total,
                            orderId = checkout.orderId!!,
                        )
                        applePay.execCardTransaction(
                            checkoutId = checkout.id!!,
                            orderId = checkout.orderId!!,
                            sellerUserUid = sellerUserUid,
                            purchaserUserUid = purchaserUserUid,
                            token = applePayParam!!.token,
                        )
                    }

                    else -> throw Error("Invalid payment method")
                }

            return ExecuteOrderResult(transaction = result.transaction, status = result.status)
        }
    }

    fun addYellLog(order: Order, checkout: Checkout, status: String) {
        // エールログの追加
        if (status == Util.PurchasedItemStatus.PAYSUCCESS.value) {
            try {
                rankingEventController.insertPurchaseLog(
                    userUuid = order.purchaserUid,
                    targetCreatorUuid = order.shop.creatorUid!!,
                    transactionId = order.transactionId!!,
                    amount = checkout.amount!!,
                    tipAmount = checkout.tip?.amount ?: 0,
                )
            } catch (e: Exception) {
                // エラーが発生しても処理を続行するためログの出力にとどめる
                logger.error(
                    """
                    Failed to insert purchase log: userUuid=${order.purchaserUid},
                    targetCreatorUuid=${order.shop.creatorUid!!},
                    transactionId=${order.transactionId!!},
                    amount=${checkout.amount!!},
                    tipAmount=${checkout.tip?.amount ?: 0}
                    """
                        .trimIndent(),
                    e,
                )
            }
        }
    }
}
