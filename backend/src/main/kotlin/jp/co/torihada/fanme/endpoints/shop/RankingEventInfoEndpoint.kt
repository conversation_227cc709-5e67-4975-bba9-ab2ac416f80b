package jp.co.torihada.fanme.endpoints.shop

import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.RankingEventInfoResponseBody
import jp.co.torihada.fanme.endpoints.BaseCreatorAccountIdentityEndpoint
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.modules.fanme.controllers.RankingEventController
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/shops/ranking_event_info")
class RankingEventInfoEndpoint : BaseCreatorAccountIdentityEndpoint() {

    @Inject lateinit var requestContext: ContainerRequestContext
    @Inject private lateinit var handler: RankingEventController
    @Inject lateinit var util: Util

    @GET
    @Path("/creator/{creator_account_identity}/active")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(RankingEventInfoResponseBody::class)
    @APIResponse(responseCode = "204", description = "No active event found")
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getCreatorActive(): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val result = handler.activeEvent(creatorUid)
            if (result.rankingEvent == null) return Response.noContent().build()
            val entity = ResponseEntity(result, "event")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
