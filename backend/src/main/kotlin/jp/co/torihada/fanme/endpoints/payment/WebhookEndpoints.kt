package jp.co.torihada.fanme.endpoints.payment

import jakarta.inject.Inject
import jakarta.ws.rs.Consumes
import jakarta.ws.rs.FormParam
import jakarta.ws.rs.POST
import jakarta.ws.rs.Path
import jakarta.ws.rs.core.Context
import jakarta.ws.rs.core.HttpHeaders
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.MailController
import jp.co.torihada.fanme.modules.payment.controllers.WebhookController
import jp.co.torihada.fanme.modules.payment.controllers.requests.WebhookRequest
import org.jboss.logging.Logger

@Path("/webhook")
class WebhookEndpoints {

    @Inject private lateinit var config: Config
    @Inject private lateinit var webhookController: WebhookController
    @Inject private lateinit var mailController: MailController
    @Inject private lateinit var logger: Logger

    @POST
    @Path("/gmo/payment/notice")
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    fun gmoWebhook(
        @FormParam("ShopID") shopId: String, // GMOのショップID
        @FormParam("ShopPass") shopPass: String,
        @FormParam("AccessID") accessId: String,
        @FormParam("AccessPass") accessPass: String,
        @FormParam("OrderID") orderId: String,
        @FormParam("Status") status: String,
        @FormParam("Amount") amount: String,
        @FormParam("Tax") tax: String,
        @FormParam("PayType") payType: String,
        @Context headers: HttpHeaders,
    ): Response {
        // X-Forwarded-Forヘッダーを取得
        val gmoIp = headers.getHeaderString("X-Forwarded-For")
        if (gmoIp != config.gmoWebhookAllowedRemoteIp()) {
            return Response.status(Response.Status.NOT_FOUND).build()
        }

        return try {
            val result =
                webhookController.gmoWebhook(
                    WebhookRequest.GmoWebhook(
                        shopId = shopId,
                        shopPass = shopPass,
                        accessId = accessId,
                        accessPass = accessPass,
                        orderId = orderId,
                        status = status,
                        amount = amount,
                        tax = tax,
                        payType = payType,
                    )
                )

            // メール送信
            if (result.transactionId != null) {
                try {
                    mailController.sendPaymentMail(transactionId = result.transactionId)
                } catch (e: Exception) {
                    logger.error("Failed to send mail", e)
                }
            }

            if (result.status != null || result.message != null) {
                Response.ok(1).build()
            } else {
                // 正常終了
                Response.ok(0).build()
            }
        } catch (e: Exception) {
            Response.status(Response.Status.INTERNAL_SERVER_ERROR).build()
        }
    }
}
