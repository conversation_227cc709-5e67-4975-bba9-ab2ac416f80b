package jp.co.torihada.fanme.endpoints.shop.request.item.digitalGacha.update

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.ForSale
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.OnSale
import jp.co.torihada.fanme.modules.shop.controllers.requests.DigitalGachaItemRequest

data class UpdateDigitalGachaItemRequest(
    val name: String,
    val description: String?,
    val thumbnailUri: String,
    val thumbnailFrom: Int,
    val thumbnailBlurLevel: Int,
    val thumbnailWatermarkLevel: Int,
    val price: Int,
    val available: Boolean,
    val itemFiles: List<DigitalGachaFileForUpdate>,
    val samples: List<DigitalGachaSampleFileForUpdate>?,
    val benefit: DigitalGachaBenefitForUpdate?,
    val tags: List<String>?,
    val itemOption: ItemOption?,
)

data class DigitalGachaFileForUpdate(val id: Long, val name: String, val isSecret: Boolean)

data class DigitalGachaBenefitForUpdate(
    val description: String?,
    val files: List<DigitalGachaBenefitFileForUpdate>?,
)

data class DigitalGachaBenefitFileForUpdate(
    val id: Long,
    val name: String,
    val objectUri: String,
    val thumbnailUri: String?,
    val fileType: String,
    val size: Float,
    val duration: Int,
    val itemThumbnailSelected: Boolean?,
    val sortOrder: Int?,
    val conditionType: Int,
)

data class DigitalGachaSampleFileForUpdate(
    val id: Long?,
    val name: String,
    val objectUri: String,
    val thumbnailUri: String?,
    val fileType: String,
    val size: Float,
    val duration: Int,
)

data class ItemOption(val password: String?, val onSale: OnSale?, val forSale: ForSale?)

@ApplicationScoped
class UpdateDigitalGachaItemRequestConverter() {
    fun requestToUpdateItem(
        creatorUid: String,
        itemId: Long,
        request: UpdateDigitalGachaItemRequest,
    ): DigitalGachaItemRequest.UpdateItem {
        return DigitalGachaItemRequest.UpdateItem(
            itemId = itemId,
            creatorUid = creatorUid,
            name = request.name,
            description = request.description,
            thumbnailUri = request.thumbnailUri,
            thumbnailFrom = request.thumbnailFrom,
            thumbnailBlurLevel = request.thumbnailBlurLevel,
            thumbnailWatermarkLevel = request.thumbnailWatermarkLevel,
            itemOption = convertItemOption(request.itemOption),
            price = request.price,
            available = request.available,
            samples = request.samples?.let { convertSampleFiles(it) },
            benefit = null,
            tags = request.tags,
            files = convertUpdateFiles(request.itemFiles),
        )
    }

    private fun convertForSale(request: ForSale?): DigitalGachaItemRequest.ForSale? {
        return request?.let {
            DigitalGachaItemRequest.ForSale(startAt = request.startAt, endAt = request.endAt)
        }
    }

    private fun convertOnSale(request: OnSale?): DigitalGachaItemRequest.OnSale? {
        return request?.let {
            DigitalGachaItemRequest.OnSale(
                discountRate = request.discountRate,
                startAt = request.startAt,
                endAt = request.endAt,
            )
        }
    }

    private fun convertItemOption(request: ItemOption?): DigitalGachaItemRequest.ItemOption? {
        return request?.let {
            DigitalGachaItemRequest.ItemOption(
                qtyTotal = null,
                forSale = convertForSale(request.forSale),
                password = request.password,
                onSale = convertOnSale(request.onSale),
            )
        }
    }

    private fun convertSampleFiles(
        request: List<DigitalGachaSampleFileForUpdate>
    ): List<DigitalGachaItemRequest.SampleFile> {
        return request.map {
            DigitalGachaItemRequest.SampleFile(
                id = it.id,
                name = it.name,
                objectUri = it.objectUri.substringBefore("?"),
                thumbnailUri = it.thumbnailUri,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
            )
        }
    }

    private fun convertUpdateFiles(
        request: List<DigitalGachaFileForUpdate>
    ): List<DigitalGachaItemRequest.UpdateFile> {
        return request.map {
            DigitalGachaItemRequest.UpdateFile(id = it.id, name = it.name, isSecret = it.isSecret)
        }
    }

    private fun convertBenefitFiles(
        request: List<DigitalGachaBenefitFileForUpdate>
    ): List<DigitalGachaItemRequest.BenefitFile> {
        return request.map {
            DigitalGachaItemRequest.BenefitFile(
                id = it.id,
                name = it.name,
                objectUri = it.objectUri.substringBefore("?"),
                thumbnailUri = it.thumbnailUri,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
                conditionType = it.conditionType,
            )
        }
    }
}
