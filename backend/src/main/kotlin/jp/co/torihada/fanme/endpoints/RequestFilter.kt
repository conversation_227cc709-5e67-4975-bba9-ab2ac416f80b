package jp.co.torihada.fanme.endpoints

import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.ext.Provider
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.odata.OData
import org.jboss.resteasy.reactive.server.ServerRequestFilter

@Provider
class RequestFilter {

    @ServerRequestFilter
    fun requestFilter(requestContext: ContainerRequestContext) {

        val uriInfo = requestContext.uriInfo

        val queryParameters = uriInfo.queryParameters
        val top = queryParameters.getFirst("\$top")
        val skip = queryParameters.getFirst("\$skip")
        if (top != null || skip != null) {
            requestContext.setProperty(
                "odata",
                OData(top = top?.toIntOrNull() ?: 10, skip = skip?.toIntOrNull() ?: 0),
            )
        }

        val accountIdentity = uriInfo.pathParameters.getFirst("creator_account_identity")
        val includeDeleted = queryParameters.getFirst("include_deleted") == "1"
        if (accountIdentity != null) {
            val creator =
                if (includeDeleted) {
                    UserController().getUserByAccountIdentity(accountIdentity)
                } else {
                    UserController().getUserByNotDeletedAccountIdentity(accountIdentity)
                }
            if (creator != null) {
                requestContext.setProperty("creator", creator)
                requestContext.setProperty("creator_uid", creator.uid)
            }
        }
    }
}
