package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetAgencies {
    data class Input(val odata: OData?)

    fun execute(params: Input): Result<List<Agency>, FanmeException> {
        return Ok(Agency.findAll(params.odata?.top, params.odata?.skip))
    }
}
