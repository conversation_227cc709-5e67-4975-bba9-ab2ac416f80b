package jp.co.torihada.fanme.endpoints.shop

import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.endpoints.BaseCreatorAccountIdentityEndpoint
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.shop.controllers.ItemPasswordUnlockCacheController

@Path("/shops/{creator_account_identity}/items/{item_id}/password-unlock")
class ItemPasswordUnlockCacheEndpoint : BaseCreatorAccountIdentityEndpoint() {

    @Inject lateinit var requestContext: ContainerRequestContext
    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var handler: ItemPasswordUnlockCacheController
    @Inject lateinit var util: Util

    @GET
    @RolesAllowed("LoginUser")
    fun getItemPasswordUnlockCache(@PathParam("item_id") itemId: Long): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            Response.ok(handler.getCache(creatorUid, itemId, userUid)).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class CreateItemPasswordUnlockCacheRequest(
        @JsonProperty("user_input_password") val userInputPassword: String
    )

    @POST
    @RolesAllowed("LoginUser")
    @Consumes("application/json")
    @Produces("application/json")
    fun createItemPasswordUnlockCache(
        @PathParam("item_id") itemId: Long,
        requestBody: CreateItemPasswordUnlockCacheRequest,
    ): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result =
                handler.createCache(creatorUid, itemId, userUid, requestBody.userInputPassword)
            val entity = ResponseEntity(result, "item_password_unlock_cache")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
