package jp.co.torihada.backend.externals.client.auth

import jakarta.enterprise.context.ApplicationScoped
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.backend.externals.entity.auth.ExtAuthSyncUserInfo
import jp.co.torihada.backend.externals.entity.auth.ExtFanmeOAuthVerifyToken
import jp.co.torihada.backend.externals.entity.auth.ExtFanmeOauthGetToken
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient

@Path("/oauth2")
@RegisterRestClient(configKey = "fanme-auth")
@ApplicationScoped
interface ExtFanmeOAuthClient {
    @POST
    @Path("/token")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    fun getToken(body: ExtFanmeOauthGetToken.Request): ExtFanmeOauthGetToken.Response

    @POST
    @Path("/verify")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    fun verifyToken(body: ExtFanmeOAuthVerifyToken.Request): ExtFanmeOAuthVerifyToken.Response

    @DELETE
    @Path("/user/delete")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    fun deleteUser(@HeaderParam("Authorization") token: String)

    @PUT
    @Path("/user/info")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    fun changeUserInfo(
        @HeaderParam("Authorization") token: String,
        body: ExtAuthSyncUserInfo.Request,
    )
}
