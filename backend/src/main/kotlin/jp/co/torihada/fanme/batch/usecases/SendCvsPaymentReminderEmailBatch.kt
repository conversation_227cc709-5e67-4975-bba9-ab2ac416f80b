package jp.co.torihada.fanme.batch.usecases

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.modules.payment.externals.BaseExternals
import jp.co.torihada.fanme.modules.payment.externals.client.auth.ExtAuthClient
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.shop.services.EmailService
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger

@ApplicationScoped
class SendCvsPaymentReminderEmailBatch {
    private val logger: Logger = Logger.getLogger(SendCvsPaymentReminderEmailBatch::class.java)
    @Inject private lateinit var emailService: EmailService
    @Inject @RestClient private lateinit var extAuthClient: ExtAuthClient
    @Inject private lateinit var baseExternals: BaseExternals

    class Output

    // 日本時間取得
    val jpNow = LocalDateTime.now(ZoneId.of("Asia/Tokyo"))
    // 当日23:59:59の日本時間を設定
    val jpNowHourEnd: LocalDateTime =
        LocalDateTime.of(jpNow.year, jpNow.month, jpNow.dayOfMonth, 23, 59, 59)

    // 6時間後、24時間後、48時間後の日本時間を Instant に変換
    val later6hrs = jpNowHourEnd.plusHours(6).atZone(ZoneId.of("Asia/Tokyo")).toInstant()
    val later24hrs = jpNowHourEnd.plusHours(24).atZone(ZoneId.of("Asia/Tokyo")).toInstant()
    val later48hrs = jpNowHourEnd.plusHours(48).atZone(ZoneId.of("Asia/Tokyo")).toInstant()

    private val laterTimes = listOf(later6hrs, later24hrs, later48hrs)

    // バッツを動かす時間帯が17:55 23:55 なのでその時間に動いていることを確認するためのフォーマット
    private val formatter = DateTimeFormatter.ofPattern("yyyyMMddHH55")

    fun execute() {
        val toBeExpiredCvsPayments = Checkout.findByPaymentTermList(laterTimes)
        val currentMoment = LocalDateTime.now(ZoneId.of("Asia/Tokyo")).format(formatter)

        logger.info("[SendReminderEmailBatch]コンビニ決済未払いリマインドメールの発送：バッチ${currentMoment}実行開始")
        if (toBeExpiredCvsPayments.isNotEmpty()) {
            processToBeExpiredCvsPayments(toBeExpiredCvsPayments, currentMoment)
        }

        logger.info("[SendReminderEmailBatch]コンビニ決済未払いリマインドメールの発送：バッチ${currentMoment}実行終了")
    }

    fun processToBeExpiredCvsPayments(
        toBeExpiredCvsPayments: List<Checkout>,
        currentMoment: String,
    ) {
        toBeExpiredCvsPayments.forEach { checkout ->
            val purchaserUserId = checkout.purchaserUserId
            val checkoutId = checkout.id
            if (purchaserUserId == null) {
                logger.info(
                    "[SendReminderEmailBatch][CheckoutId: ${checkoutId}]コンビニ決済未払いリマインドメールの発送：購入者IDが存在しません"
                )
                return@forEach
            }

            try {
                val authorization = baseExternals.getAuthorization()
                val userInfo =
                    extAuthClient.retrieveAuthInfo(
                        uuid = purchaserUserId,
                        authorization = authorization,
                    )
                val cvsPaymentDeadLine = checkout.paymentTerm?.atZone(ZoneId.of("Asia/Tokyo"))
                val cvsPaymentDeadLineYear = cvsPaymentDeadLine?.year
                val cvsPaymentDeadLineMonth = String.format("%02d", cvsPaymentDeadLine?.monthValue)
                val cvsPaymentDeadLineDay = String.format("%02d", cvsPaymentDeadLine?.dayOfMonth)

                if (
                    checkout.convenience == null ||
                        checkout.receiptNo == null ||
                        checkout.confNo == null
                ) {
                    logger.error(
                        "[SendReminderEmailBatch][CheckoutId: ${checkoutId}]コンビニ決済未払いリマインドメールの発送：コンビニ決済情報が存在しません"
                    )
                    return@forEach
                } else {
                    try {
                        if (userInfo.email == null) {
                            return@forEach
                        }
                        emailService.sendCvsPaymentReminderMail(
                            purchaserEmail = userInfo.email,
                            purchaserName = userInfo.name,
                            convenience = checkout.convenience!!,
                            receiptNo = checkout.receiptNo!!,
                            confNo = checkout.confNo!!,
                            cvsPaymentDeadlineDay = cvsPaymentDeadLineDay,
                            cvsPaymentDeadlineMonth = cvsPaymentDeadLineMonth,
                            cvsPaymentDeadlineYear = cvsPaymentDeadLineYear.toString(),
                        )

                        logger.info(
                            "[SendReminderEmailBatch][CheckoutId: ${checkoutId}]コンビニ決済未払いリマインドメールの発送：送信成功しました: ${userInfo.email}"
                        )
                        return@forEach
                    } catch (e: Exception) {
                        logger.error(
                            "[SendReminderEmailBatch][CheckoutId: ${checkoutId}]コンビニ決済未払いリマインドメールの発送：送信失敗しました: ${userInfo.email}"
                        )
                        return@forEach
                    }
                }
            } catch (e: Exception) {
                logger.error(
                    "[SendReminderEmailBatch][CheckoutId: ${checkoutId}]コンビニ決済未払いリマインドメールの発送：購入者情報の取得に失敗しました",
                    e,
                )
                return@forEach
            }
        }
    }
}
