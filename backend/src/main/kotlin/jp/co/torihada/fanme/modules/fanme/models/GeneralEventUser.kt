package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "general_event_users")
class GeneralEventUser : BaseModel() {
    @NotNull
    @ManyToOne
    @JoinColumn(name = "general_event_id", nullable = false)
    var generalEvent: GeneralEvent? = null

    @NotNull @ManyToOne @JoinColumn(name = "user_id", nullable = false) var user: User? = null

    @Column(name = "rank", nullable = false) var rank: Int = 0

    @Column(name = "yell_count", nullable = false) var yellCount: Int = 0

    @Column(name = "aggregation_at")
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var aggregationAt: Instant? = null

    @Column(name = "previous_rank", nullable = false) var previousRank: Int = 0

    @Column(name = "previous_yell_count", nullable = false) var previousYellCount: Int = 0

    @Column(name = "previous_aggregation_at")
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var previousAggregationAt: Instant? = null

    companion object : PanacheCompanion<GeneralEventUser> {
        fun findActiveUsers(generalEventId: Long): List<GeneralEventUser> {
            return find("generalEventId", generalEventId).list()
        }
    }
}
