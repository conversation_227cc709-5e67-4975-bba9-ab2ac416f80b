package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull

@Entity
@Table(name = "user_general_event_fan_badges")
class UserGeneralEventFanBadge : BaseModel() {
    @NotNull
    @ManyToOne
    @JoinColumn(name = "general_event_id", nullable = false)
    var generalEvent: GeneralEvent? = null

    @NotNull @Column(name = "rank", nullable = false) var rank: Int = 0

    @NotNull @ManyToOne @JoinColumn(name = "user_id", nullable = false) var user: User? = null

    companion object : PanacheCompanion<UserGeneralEventFanBadge> {
        fun insertUserGeneralEventFanBadge(generalEvent: GeneralEvent, rank: Int, user: User) {
            val badge =
                UserGeneralEventFanBadge().apply {
                    this.generalEvent = generalEvent
                    this.rank = rank
                    this.user = user
                }
            badge.persist()
        }
    }
}
