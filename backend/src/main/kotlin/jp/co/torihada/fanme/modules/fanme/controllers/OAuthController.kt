package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.getOrElse
import io.quarkus.logging.Log
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.Response
import jakarta.ws.rs.core.UriBuilder
import java.net.URI
import java.time.LocalDate
import java.time.ZoneOffset
import jp.co.torihada.backend.externals.client.auth.ExtFanmeOAuthClient
import jp.co.torihada.backend.externals.entity.auth.ExtFanmeOAuthVerifyToken
import jp.co.torihada.backend.externals.entity.auth.ExtFanmeOauthGetToken
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.fanme.Config as FanmeConfig
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.models.*
import org.eclipse.microprofile.rest.client.inject.RestClient

@ApplicationScoped
class OAuthController : BaseController() {

    @Inject private lateinit var commonConfig: CommonConfig
    @Inject private lateinit var fanmeConfig: FanmeConfig

    @Inject @RestClient private lateinit var extFanmeOAuthClient: ExtFanmeOAuthClient

    fun fanme(returnUrl: String?, proc: String?): Response {
        val state = generateTokenString()
        val nonce = generateTokenString()

        val url =
            UriBuilder.fromUri("${fanmeConfig.authServerUrl()}/oauth2/authorize")
                .queryParam("client_id", fanmeConfig.fanmeAuthClientId())
                .queryParam("redirect_uri", "${commonConfig.hostUrl()}/auth/fanme/callback")
                .queryParam("response_type", "code")
                .queryParam("scope", "email profile openid")
                .queryParam("state", state)
                .queryParam("nonce", nonce)
                .queryParam("proc", proc ?: "")
                .queryParam("return_url", returnUrl ?: "")
                .build()
                .toString()

        val stateCookie = generateCookie("state", state)
        val nonceCookie = generateCookie("nonce", nonce)

        return Response.seeOther(UriBuilder.fromUri(url).build())
            .cookie(stateCookie)
            .cookie(nonceCookie)
            .build()
    }

    @Transactional
    fun callback(
        code: String,
        state: String,
        returnUrl: String,
        stateCookie: String?,
        nonceCookie: String?,
        loginFailureCount: String?,
    ): Response {
        if (stateCookie.isNullOrEmpty() || state != stateCookie) {
            return redirectFailureUri(loginFailureCount, returnUrl)
        }
        val authClientId = fanmeConfig.fanmeAuthClientId()
        // トークン取得
        val tokens =
            extFanmeOAuthClient.getToken(
                ExtFanmeOauthGetToken.Request(
                    code = code,
                    clientId = authClientId,
                    clientSecret = fanmeConfig.fanmeAuthClientSecret(),
                    grantType = "authorization_code",
                    redirectUri = "${commonConfig.hostUrl()}/auth/fanme/callback",
                )
            )
        // トークン検証
        val claims =
            extFanmeOAuthClient.verifyToken(
                ExtFanmeOAuthVerifyToken.Request(
                    idToken = tokens.idToken,
                    clientId = authClientId,
                    nonce = nonceCookie,
                )
            )
        // iss検証
        if (claims.iss != fanmeConfig.authServerUrl()) {
            return Response.status(Response.Status.BAD_REQUEST).build()
        }

        val user =
            findOrInitUser(claims.name, claims.sub).getOrElse {
                return redirectFailureUri(loginFailureCount, returnUrl)
            }

        val userToken =
            upsertUserToken(user, tokens.idToken).getOrElse {
                return redirectFailureUri(loginFailureCount, returnUrl)
            }

        val loginStateCookie = generateCookie("creator_login_state", userToken.id.toString())
        val deleteStateCookie = deleteCookie("state")
        val deleteNonceCookie = deleteCookie("nonce")
        return Response.seeOther(genRedirectUri(user, returnUrl))
            .cookie(deleteStateCookie)
            .cookie(deleteNonceCookie)
            .cookie(loginStateCookie)
            .build()
    }

    fun findOrInitUser(name: String?, uid: String): Result<User, Exception> {
        return try {
            val existingUser = User.findByUuid(uid)

            if (existingUser != null) {
                if (name != null) {
                    existingUser.name = name
                }
                existingUser.persist()
                Ok(existingUser)
            } else {
                val newUser =
                    User().apply {
                        this.name = name ?: Const.FANME_DEFAULT_USER_NAME
                        this.accountIdentity = generateRandomHex(10)
                        this.uid = uid
                        this.isPublic = true
                        this.birthday =
                            LocalDate.of(1900, 1, 1).atStartOfDay().toInstant(ZoneOffset.UTC)
                        this.birthdayConfirmed = true
                        this.gender = Const.Gender.BLANK.value
                        this.purpose = Const.Purpose.Creator.value
                        this.filledProfile = true
                    }
                newUser.persist()
                initCreatorProfile(newUser).getOrElse {
                    Log.error(it)
                    return Err(it)
                }

                Ok(newUser)
            }
        } catch (e: Exception) {
            Log.error(e)
            Err(e)
        }
    }

    fun initCreatorProfile(user: User): Result<Unit, Exception> {
        return try {
            val profile =
                Profile().apply {
                    this.user = user
                    this.bio = ""
                    this.snsLinkColor = Const.SnsLinkColor.ORIGINAL.value
                    this.officialFlg = false
                }
            val profileThemeColor =
                ProfileThemeColor().apply {
                    this.profile = profile
                    this.themeColorId = Const.ThemeColor.DARK.value
                }
            val profileCover =
                ProfileCover().apply {
                    this.profile = profile
                    this.coverVisibility = true
                    this.brightness = Const.Brightness.NORMAL.value
                }
            profile.persist()
            profileThemeColor.persist()
            profileCover.persist()
            Ok(Unit)
        } catch (e: Exception) {
            Err(e)
        }
    }

    // トークン保存
    fun upsertUserToken(user: User, idToken: String): Result<UserToken, Exception> {
        return try {
            if (user.token == null) {
                val userToken =
                    UserToken().apply {
                        this.user = user
                        this.idToken = idToken
                    }
                userToken.persist()
                Ok(userToken)
            } else {
                user.token?.idToken = idToken
                user.token?.persist()
                Ok(user.token!!)
            }
        } catch (e: Exception) {
            Err(e)
        }
    }

    fun genRedirectUri(user: User, returnUrl: String): URI {
        val baseUrl =
            if (returnUrl.isEmpty()) {
                URI("${fanmeConfig.fanmeFrontendUrl()}/@${user.accountIdentity}")
            } else {
                URI(returnUrl)
            }
        return URI(UriBuilder.fromUri(baseUrl).queryParam("authorize", "true").build().toString())
    }

    fun redirectFailureUri(loginFailureCountStr: String?, returnUrl: String): Response {
        val loginFailureCount = loginFailureCountStr?.toIntOrNull()
        val deleteStateCookie = deleteCookie("state")
        val deleteNonceCookie = deleteCookie("nonce")
        return if (loginFailureCount === null) {
            val failureCountCookie = generateCookie("login_failure_count", "1")
            val uri =
                URI(
                    UriBuilder.fromUri("${commonConfig.hostUrl()}/creators/auth/fanme")
                        .queryParam("return_url")
                        .build()
                        .toString()
                )
            Response.seeOther(uri)
                .cookie(failureCountCookie)
                .cookie(deleteStateCookie)
                .cookie(deleteNonceCookie)
                .build()
        } else {
            val deleteCookie = deleteCookie("login_failure_count")
            val uri =
                URI(
                    UriBuilder.fromUri("${fanmeConfig.fanmeFrontendUrl()}/unauthorized")
                        .build()
                        .toString()
                )
            Response.seeOther(uri)
                .cookie(deleteCookie)
                .cookie(deleteStateCookie)
                .cookie(deleteNonceCookie)
                .build()
        }
    }
}
