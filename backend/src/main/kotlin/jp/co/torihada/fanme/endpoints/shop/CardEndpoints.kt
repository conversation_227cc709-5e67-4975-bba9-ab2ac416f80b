package jp.co.torihada.fanme.endpoints.shop

import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.shop.controllers.CardController

@Path("/cards")
class CardEndpoints {
    @Inject private lateinit var cardController: CardController
    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var util: Util

    @GET
    @RolesAllowed("LoginUser")
    @Produces("application/json")
    fun fetchCard(): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = cardController.fetchCard(userId = userUid)
            val entity = ResponseEntity(result, "cards")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class RegisterCardRequest(
        @JsonProperty("card_name") val cardName: String,
        @JsonProperty("token") val token: String,
    )

    @POST
    @Produces("application/json")
    fun registerCard(request: RegisterCardRequest): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result =
                cardController.registerCard(
                    cardName = request.cardName,
                    token = request.token,
                    userId = userUid,
                )
            val entity = ResponseEntity(result, "card")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class UpdateCardRequest(
        @JsonProperty("card_sequence") val cardSequence: Int,
        @JsonProperty("card_name") val cardName: String,
        @JsonProperty("card_holder_name") val cardHolderName: String,
        @JsonProperty("expire") val expire: String,
    )

    @PUT
    @Path("/update")
    @Consumes("application/json")
    @Produces("application/json")
    fun updateCard(request: UpdateCardRequest): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result =
                cardController.updateCard(
                    cardSequence = request.cardSequence,
                    cardName = request.cardName,
                    cardHolderName = request.cardHolderName,
                    expire = request.expire,
                    userId = userUid,
                )
            val entity = ResponseEntity(result, "card")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @DELETE
    @Path("/{card_sequence}")
    @Produces("application/json")
    fun deleteCard(@PathParam("card_sequence") cardSequence: Int): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = cardController.deleteCard(cardSequence = cardSequence, userId = userUid)
            val entity = ResponseEntity(result, "result")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
