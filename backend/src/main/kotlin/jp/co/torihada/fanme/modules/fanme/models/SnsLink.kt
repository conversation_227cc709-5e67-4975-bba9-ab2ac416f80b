package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "sns_links")
class SnsLink : BaseModel() {
    @NotNull
    @ManyToOne(optional = false)
    @JoinColumn(name = "profile_id", nullable = false)
    var profile: Profile? = null

    @Size(max = 255) @NotNull @Column(name = "type", nullable = false) var type: String? = null

    @Size(max = 255)
    @NotNull
    @Column(name = "account_identity", nullable = false)
    var accountIdentity: String? = null

    @OneToOne(mappedBy = "snsLink", orphanRemoval = true) var snsLinkDisplay: SnsLinkDisplay? = null
}
