package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import jp.co.torihada.fanme.modules.shop.Const

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "user_yell_logs")
class UserYellLog : BaseModel() {

    @ManyToOne @JoinColumn(name = "user_id", nullable = false) var user: User? = null

    @ManyToOne
    @JoinColumn(name = "target_creator_id", nullable = false)
    var targetCreator: User? = null

    // 0=クリック, 1=購入, 2=シェア
    @Column(name = "log_type", nullable = false) var logType: Int? = null

    // Default value is 1
    @Column(name = "yell_count", nullable = false) var yellCount: Int? = null

    @Column(name = "transaction_id") var transactionId: Long? = null

    @Column(name = "action_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var actionAt: Instant? = null

    companion object : PanacheCompanion<UserYellLog> {
        fun findTodayYellLog(user: User, targetCreator: User, logType: Int): UserYellLog? {
            val today =
                LocalDateTime.now()
                    .withHour(0)
                    .withMinute(0)
                    .withSecond(0)
                    .withNano(0)
                    .toInstant(ZoneOffset.UTC)
            return find(
                    "user = ?1 and targetCreator = ?2 and logType = ?3 and actionAt >= ?4",
                    user,
                    targetCreator,
                    logType,
                    today,
                )
                .firstResult()
        }

        fun findUserYellLogToCreatorToday(user: User, creator: User): List<UserYellLog> {
            val today =
                LocalDateTime.now()
                    .withHour(0)
                    .withMinute(0)
                    .withSecond(0)
                    .withNano(0)
                    .toInstant(ZoneOffset.UTC)
            return find("user = ?1 and targetCreator = ?2 and actionAt >= ?3", user, creator, today)
                .list()
        }

        // -1=>2.5%, 100=>44.0%, 200=>47.3%, 500=>4.0%, 1000=>2.0%, 10000=>0.2% の確率で出る
        fun drawGacha(): Int {
            val balls =
                mutableListOf<Int>().apply {
                    addAll(List(25) { -1 })
                    addAll(List(440) { 100 })
                    addAll(List(473) { 200 })
                    addAll(List(40) { 500 })
                    addAll(List(20) { 1000 })
                    addAll(List(2) { 10000 })
                }
            return balls.random()
        }

        fun insertPurchaseLog(
            user: User,
            targetCreator: User,
            transactionId: Long,
            amount: Int,
            tipAmount: Int? = null,
        ) {
            val now = Instant.now()
            if (user != null && targetCreator != null) {
                UserYellLog().apply {
                    this.user = user
                    this.targetCreator = targetCreator
                    this.actionAt = now
                    this.logType = LogType.PURCHASE.value
                    this.yellCount = amount
                    this.transactionId = transactionId
                    persist()
                }

                if (tipAmount != null && tipAmount > 0) {
                    UserYellLog().apply {
                        this.user = user
                        this.targetCreator = targetCreator
                        this.actionAt = now
                        this.logType = LogType.TIP.value
                        this.yellCount = tipAmount
                        this.transactionId = transactionId
                        persist()
                    }
                }
            }
        }
    }

    enum class LogType(val value: Int) {
        CLICK(0),
        PURCHASE(1),
        SHARE(2),
        TIP(3),
        GACHA(4),
    }
}
