package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonBackReference
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "profile_theme_colors")
class ProfileThemeColor : BaseModel() {
    @OneToOne(optional = false)
    @JsonBackReference
    @JoinColumn(name = "profile_id", nullable = false)
    var profile: Profile? = null

    @NotNull @Column(name = "theme_color_id", nullable = false) var themeColorId: Long = 0

    @Size(max = 255) @Column(name = "custom_color") var customColor: String? = null
}
