package jp.co.torihada.fanme.modules.fanme.controllers.responses

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.enterprise.context.ApplicationScoped
import java.time.Instant
import jp.co.torihada.fanme.modules.fanme.Const

@ApplicationScoped
class UserResponse {

    data class User(
        @JsonProperty("id") val id: Long?,
        @JsonProperty("uid") val uid: String?,
        @JsonProperty("name") val name: String?,
        @JsonProperty("account_identity") val accountIdentity: String?,
        @JsonProperty("is_public") val isPublic: Boolean?,
        @JsonProperty("birthday") val birthday: Instant?,
        @JsonProperty("birthday_confirmed") val birthdayConfirmed: Boolean?,
        @JsonProperty("gender") val gender: String?,
        @JsonProperty("icon") var icon: String?,
        @JsonProperty("filled_profile") val filledProfile: Boolean?,
        @JsonProperty("allow_public_sharing") val allowPublicSharing: Boolean?,
        @JsonProperty("purpose") val purpose: Int?,
        @JsonProperty("profile") val profile: Profile,
    ) {
        constructor(
            user: jp.co.torihada.fanme.modules.fanme.models.User
        ) : this(
            id = user.id,
            uid = user.uid,
            name = user.name,
            accountIdentity = user.accountIdentity,
            isPublic = user.isPublic,
            birthday = user.birthday,
            birthdayConfirmed = user.birthdayConfirmed,
            gender = user.gender,
            icon = user.iconUrl,
            filledProfile = user.filledProfile,
            allowPublicSharing = user.allowPublicSharing,
            purpose = user.purpose,
            profile =
                Profile(
                    bio = user.profile?.bio,
                    headerImage = user.profile?.headerImage,
                    covers =
                        Profile.Cover(
                            brightness = user.profile?.covers?.brightness,
                            coverVisibility = user.profile?.covers?.coverVisibility,
                            resources =
                                user.profile?.covers?.coverImage?.map {
                                    Profile.Cover.Resource(
                                        displayable = it.displayableCoverImage != null,
                                        resource = it.resource,
                                        resourceType = it.resourceType,
                                    )
                                },
                        ),
                    snsLinkColor = user.profile?.snsLinkColor,
                    officialFlg = user.profile?.officialFlg,
                    themeColor =
                        user.profile?.themeColor?.let {
                            if (it.themeColorId == Const.ThemeColor.CUSTOM.value) {
                                it.customColor
                            } else {
                                Const.ThemeColor.fromValue(it.themeColorId)?.str
                            }
                        },
                ),
        )

        data class Profile(
            @JsonProperty("bio") val bio: String?,
            @JsonProperty("header_image") val headerImage: String?,
            @JsonProperty("covers") val covers: Cover,
            @JsonProperty("sns_link_color") val snsLinkColor: String?,
            @JsonProperty("official_flg") val officialFlg: Boolean?,
            @JsonProperty("theme_color") val themeColor: String?,
        ) {
            constructor(
                profile: jp.co.torihada.fanme.modules.fanme.models.Profile
            ) : this(
                bio = profile.bio,
                headerImage = profile.headerImage,
                covers =
                    Cover(
                        brightness = profile.covers?.brightness,
                        coverVisibility = profile.covers?.coverVisibility,
                        resources =
                            profile.covers?.coverImage?.map {
                                Cover.Resource(
                                    displayable = it.displayableCoverImage != null,
                                    resource = it.resource,
                                    resourceType = it.resourceType,
                                )
                            },
                    ),
                snsLinkColor = profile.snsLinkColor,
                officialFlg = profile.officialFlg,
                themeColor =
                    profile.themeColor?.let {
                        if (it.themeColorId == Const.ThemeColor.CUSTOM.value) {
                            it.customColor
                        } else {
                            Const.ThemeColor.fromValue(it.themeColorId)?.str
                        }
                    },
            )

            data class Cover(
                @JsonProperty("brightness") val brightness: String?,
                @JsonProperty("cover_visibility") val coverVisibility: Boolean?,
                @JsonProperty("resources") val resources: List<Resource>?,
            ) {
                data class Resource(
                    @JsonProperty("displayable") val displayable: Boolean?,
                    @JsonProperty("resource") val resource: String?,
                    @JsonProperty("resource_type") val resourceType: String?,
                )
            }
        }
    }

    data class IdTokenResponse(@JsonProperty("id_token") val idToken: String?)

    data class ContentBlocks(
        @JsonProperty("content_blocks") val contentBlocks: List<ContentBlock>
    ) {
        data class ContentBlock(
            @JsonProperty("id") val id: Int,
            @JsonProperty("type") val type: String,
            @JsonProperty("content") val content: String,
            @JsonProperty("created_at") val createdAt: String,
            @JsonProperty("updated_at") val updatedAt: String,
        )
    }

    data class UidsResponse(
        @JsonProperty("creator_uid") val creatorUid: String,
        @JsonProperty("user_uid") val userUid: String,
    )
}
