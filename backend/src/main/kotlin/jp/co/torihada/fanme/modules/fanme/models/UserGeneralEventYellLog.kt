package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const

@Entity
@Table(name = "user_general_event_yell_logs")
class UserGeneralEventYellLog : BaseModel() {
    @NotNull @ManyToOne @JoinColumn(name = "user_id", nullable = false) var user: User? = null

    @NotNull
    @ManyToOne
    @JoinColumn(name = "target_creator_id", nullable = false)
    var targetCreator: User? = null

    // 0=クリック, 1=購入, 2=シェア
    @NotNull @Column(name = "log_type", nullable = false) var logType: LogType = LogType.CLICK

    @NotNull @Column(name = "yell_count", nullable = false) var yellCount: Int = 1

    @Column(name = "transaction_id") var transactionId: Long? = null

    @NotNull
    @Column(name = "action_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var actionAt: Instant? = null

    companion object : PanacheCompanion<UserGeneralEventYellLog> {
        fun insertPurchaseLog(
            user: User,
            targetCreator: User,
            transactionId: Long,
            amount: Int,
            tipAmount: Int? = null,
        ) {
            val now = Instant.now()
            persist(
                UserGeneralEventYellLog().apply {
                    this.user = user
                    this.targetCreator = targetCreator
                    this.actionAt = now
                    this.logType = LogType.PURCHASE
                    this.yellCount = amount
                    this.transactionId = transactionId
                }
            )
            if (tipAmount != null && tipAmount > 0) {
                persist(
                    UserGeneralEventYellLog().apply {
                        this.user = user
                        this.targetCreator = targetCreator
                        this.actionAt = now
                        this.logType = LogType.TIP
                        this.yellCount = tipAmount
                        this.transactionId = transactionId
                    }
                )
            }
        }
    }

    enum class LogType(val value: Int) {
        CLICK(0),
        PURCHASE(1),
        SHARE(2),
        TIP(3),
    }
}
