package jp.co.torihada.backend.externals.payment

class Condition {
    companion object {
        fun builder(): Condition {
            return Condition()
        }
    }

    var conditions: QueryCondition? = null

    sealed class QueryCondition {
        open class StructuredCondition(val operator: String, val conditions: List<QueryCondition>) :
            QueryCondition()

        open class Condition(val key: String, val operator: String, val values: List<String>) :
            QueryCondition()
    }

    enum class QueryConditionOperator(val value: String) {
        EqualTo("=="),
        GreaterThan(">"),
        GreaterThanOrEqualTo(">="),
        In("in"),
        Less<PERSON>han("<"),
        LessThanOrEqualTo("<="),
        NotEqualTo("!="),
        NotIn("not-in"),
        Exists("exists"),
    }

    fun addOr(operator: QueryConditionOperator, queryCondition: QueryCondition): Condition {
        conditions =
            QueryCondition.StructuredCondition(operator = "OR", conditions = listOf(queryCondition))
        return this
    }
}
