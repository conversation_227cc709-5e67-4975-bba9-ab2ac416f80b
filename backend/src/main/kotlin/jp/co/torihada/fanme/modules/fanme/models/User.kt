package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonManagedReference
import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.enterprise.context.ApplicationScoped
import jakarta.enterprise.inject.spi.CDI
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import java.time.Instant
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.fanme.Config as FanmeConfig
import jp.co.torihada.fanme.modules.fanme.Const

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "creators")
class User : BaseModel() {

    @Size(max = 255) @Column(name = "icon") var icon: String? = null

    @Size(max = 255) @NotNull @Column(name = "name", nullable = false) var name: String? = null

    @Size(max = 255) @NotNull @Column(name = "gender", nullable = false) var gender: String? = null

    @NotNull
    @Column(name = "birthday", nullable = false, columnDefinition = "date")
    @JsonFormat(
        pattern = jp.co.torihada.fanme.modules.shop.Const.DATE_RESPONSE_FORMAT,
        timezone = jp.co.torihada.fanme.modules.shop.Const.DEFAULT_LOCAL_TIME_ZONE,
    )
    var birthday: Instant? = null

    @NotNull
    @Column(name = "birthday_confirmed", nullable = false)
    var birthdayConfirmed: Boolean? = false

    @Size(max = 255)
    @NotNull
    @Column(name = "account_identity", nullable = false)
    var accountIdentity: String? = null

    @NotNull
    @JsonProperty("public")
    @Column(name = "is_public", nullable = false)
    var isPublic: Boolean? = false

    @Column(name = "allow_public_sharing") var allowPublicSharing: Boolean? = null

    @Size(max = 255) @Column(name = "uid") var uid: String? = null

    @Column(name = "deleted_at")
    @JsonFormat(
        pattern = jp.co.torihada.fanme.modules.shop.Const.DATE_RESPONSE_FORMAT,
        timezone = jp.co.torihada.fanme.modules.shop.Const.DEFAULT_LOCAL_TIME_ZONE,
    )
    var deletedAt: Instant? = null

    @NotNull @Column(name = "filled_profile", nullable = false) var filledProfile: Boolean? = false

    @NotNull @Column(name = "purpose", nullable = false) var purpose: Int? = null

    @JsonIgnore
    @JsonManagedReference
    @OneToOne(mappedBy = "user", orphanRemoval = true)
    var profile: Profile? = null

    @JsonIgnore @OneToOne(mappedBy = "user", orphanRemoval = true) var token: UserToken? = null

    @JsonIgnore
    @OneToMany(mappedBy = "user", orphanRemoval = true)
    var state: MutableSet<UserState> = mutableSetOf()

    @JsonIgnore
    @OneToMany(mappedBy = "user", orphanRemoval = true)
    var articleReads: MutableSet<ArticleRead> = mutableSetOf()

    @JsonIgnore
    @OneToMany(mappedBy = "user", orphanRemoval = true)
    var userTutorials: MutableSet<UserTutorial> = mutableSetOf()

    @JsonIgnore
    @OneToOne(mappedBy = "user", orphanRemoval = true)
    var withdrawal: UserWithdrawal? = null

    @JsonIgnore
    @OneToMany(mappedBy = "user", orphanRemoval = true)
    var contentBlocks: MutableSet<ContentBlock> = mutableSetOf()

    @JsonIgnore @OneToOne(mappedBy = "user", orphanRemoval = true) var popup: CreatorPopup? = null

    @JsonIgnore
    @OneToMany(mappedBy = "creator", orphanRemoval = true)
    var rankingEventCreators: MutableSet<RankingEventCreator> = mutableSetOf()

    @JsonIgnore
    @OneToMany(mappedBy = "user", orphanRemoval = true)
    var rankingEventUsers: MutableSet<RankingEventUser> = mutableSetOf()

    @JsonIgnore
    @OneToMany(mappedBy = "user", orphanRemoval = true)
    var userRankingFanBadges: MutableSet<UserRankingFanBadge> = mutableSetOf()

    @JsonIgnore
    @OneToMany(mappedBy = "creator", orphanRemoval = true)
    var creatorRankingFanBadges: MutableSet<UserRankingFanBadge> = mutableSetOf()

    val iconUrl: String
        get() {
            val commonConfig = CDI.current().select(CommonConfig::class.java).get()
            val fanmeConfig = CDI.current().select(FanmeConfig::class.java).get()
            return if (icon == null) {
                "${fanmeConfig.fanmeApiServerUrl()}/${Const.FANME_DEFAULT_CREATOR_ICON_PATH}"
            } else {
                return if (commonConfig.envKind() == "dev") {
                    "${fanmeConfig.fanmeApiServerUrl()}/uploads/creator/icon/$id/$icon"
                } else {
                    "${fanmeConfig.s3Endpoint()}/${fanmeConfig.s3BucketName()}/${Const.S3_PATH}/creator/icon/$id/$icon"
                }
            }
        }

    @ApplicationScoped
    companion object : PanacheCompanion<User> {

        fun findByUuid(uuid: String): User? {
            return find("uid", uuid).firstResult()
        }

        fun findByUuids(uuids: List<String>): List<User> {
            return find("uid in ?1", uuids).list()
        }

        fun findNotDeletedByUuids(uuids: List<String>): List<User> {
            return find("uid in ?1 and deletedAt is null", uuids).list()
        }

        fun findNotDeletedByUuid(uuid: String): User? {
            return find("uid = ?1 and deletedAt is null", uuid).firstResult()
        }

        fun findByAccountIdentity(accountIdentity: String): User? {
            return find("accountIdentity", accountIdentity).firstResult()
        }

        fun findNotDeletedByAccountIdentity(accountIdentity: String): User? {
            return find("accountIdentity = ?1 and deletedAt is null", accountIdentity).firstResult()
        }

        fun findAccountIdentityTaken(accountIdentity: String, creatorId: Long): Boolean {
            return find("accountIdentity = ?1 and id != ?2", accountIdentity, creatorId).count() > 0
        }
    }
}
