package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnore
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import io.quarkus.hibernate.orm.panache.kotlin.PanacheQuery
import jakarta.persistence.*
import java.math.BigDecimal
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "ranking_yell_boosts")
class RankingYellBoost : BaseModel() {

    @ManyToOne
    @JsonIgnore
    @JoinColumn(name = "ranking_event_id", nullable = false)
    var rankingEvent: RankingEvent? = null

    @Column(name = "start_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var startAt: Instant? = null

    @Column(name = "end_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var endAt: Instant? = null

    @Column(name = "boost_ratio", nullable = false, columnDefinition = "DECIMAL(10, 0)")
    var boostRatio: BigDecimal? = null

    companion object : PanacheCompanion<RankingYellBoost> {
        fun findActiveBoost(rankingEvent: RankingEvent): RankingYellBoost? {
            val now = Instant.now()
            return find(
                    "rankingEvent = ?1 and startAt <= ?2 and endAt >= ?3",
                    rankingEvent,
                    now,
                    now,
                )
                .firstResult()
        }

        fun active(): PanacheQuery<RankingYellBoost> {
            val now = Instant.now()
            return find("startAt <= ?1 and endAt >= ?1", now)
        }
    }
}
