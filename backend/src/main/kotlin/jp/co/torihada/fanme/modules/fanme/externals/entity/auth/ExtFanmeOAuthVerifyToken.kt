package jp.co.torihada.backend.externals.entity.auth

import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.runtime.annotations.RegisterForReflection

@RegisterForReflection
class ExtFanmeOAuthVerifyToken {
    data class Request(
        @JsonProperty("access_token") val accessToken: String? = null,
        @JsonProperty("id_token") val idToken: String,
        @JsonProperty("client_id") val clientId: String,
        @JsonProperty("nonce") val nonce: String?,
    )

    data class Response(
        @JsonProperty("iss") val iss: String,
        @JsonProperty("sub") val sub: String,
        @JsonProperty("aud") val aud: String,
        @JsonProperty("exp") val exp: Long,
        @JsonProperty("iat") val iat: Long,
        @JsonProperty("nonce") val nonce: String? = null,
        @JsonProperty("name") val name: String? = null,
        @JsonProperty("picture") val picture: String? = null,
        @JsonProperty("email") val email: String? = null,
    )
}
