package jp.co.torihada.backend.endpoints.filter

import io.quarkus.logging.Log
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.container.ContainerResponseContext
import jakarta.ws.rs.container.ContainerResponseFilter
import jakarta.ws.rs.ext.Provider
import jp.co.torihada.fanme.modules.fanme.lib.ErrorResponse

@Provider
class ErrorResponseFilter : ContainerResponseFilter {
    override fun filter(
        requestContext: ContainerRequestContext,
        responseContext: ContainerResponseContext,
    ) {
        val entity = responseContext.entity
        if (entity is ErrorResponse) {
            responseContext.status = entity.code
            // エラーが発生した場合にログを出力
            Log.error("Error occurred: Code = ${entity.code}, Message = ${entity.message}")
        }
    }
}
