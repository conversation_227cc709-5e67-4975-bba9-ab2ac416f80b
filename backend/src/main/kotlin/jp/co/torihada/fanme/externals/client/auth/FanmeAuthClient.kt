package jp.co.torihada.fanme.externals.client.auth

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.enterprise.context.ApplicationScoped
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient

@ApplicationScoped
@RegisterRestClient(configKey = "auth")
interface FanmeAuthClient {

    data class AuthUser(
        @JsonProperty("name") val name: String? = null,
        @JsonProperty("email") val email: String? = null,
        @JsonProperty("login_email") val loginEmail: String? = null,
        @JsonProperty("external_auth") val externalAuth: Boolean? = null,
        @JsonProperty("external_auth_type") val externalAuthType: String? = null,
        @JsonProperty("providers") val providers: List<String>? = null,
    )

    @GET
    @Path("/user/info")
    @Consumes(MediaType.APPLICATION_JSON)
    fun getAuthUser(
        @QueryParam("uuid") uuid: String,
        @HeaderParam("Authorization") authorization: String,
    ): AuthUser
}
