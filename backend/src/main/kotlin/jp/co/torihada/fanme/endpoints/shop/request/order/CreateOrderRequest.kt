package jp.co.torihada.fanme.endpoints.shop.request.order

import com.fasterxml.jackson.annotation.JsonAlias
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.shop.Util.PaymentMethod
import jp.co.torihada.fanme.modules.shop.controllers.requests.OrderRequest

data class CreateOrderRequest(
    @JsonAlias("cart_id") val cartId: Long,
    @JsonAlias("cart_item_ids") val cartItemIds: List<Long>,
    @JsonAlias("tip") val tip: Int,
    @JsonAlias("payment_method") val paymentMethod: String, // Util.PaymentMethodではなくStringで受け取る
    @JsonAlias("card_param") val cardParam: CardParam?,
    @JsonAlias("convenience_param") val convenienceParam: ConvenienceParam?,
    @JsonAlias("google_pay_param") val googlePayParam: GooglePayParam?,
    @JsonAlias("apple_pay_param") val applePayParam: ApplePayParam?,
)

data class CardParam(@JsonAlias("card_sequence") val cardSequence: Int)

data class ConvenienceParam(
    @JsonAlias("convenience") val convenience: String,
    @JsonAlias("customer_name") val customerName: String,
    @JsonAlias("customer_kana") val customerKana: String,
    @JsonAlias("tel_no") val telNo: String,
)

data class GooglePayParam(@JsonAlias("token") val token: String)

data class ApplePayParam(@JsonAlias("token") val token: String)

@ApplicationScoped
class RequestConverter() {
    fun requestToCreateOrder(
        uid: String,
        requestBody: CreateOrderRequest,
    ): OrderRequest.CreateOrder {
        return OrderRequest.CreateOrder(
            userId = uid,
            cartId = requestBody.cartId,
            cartItemIds = requestBody.cartItemIds,
            tip = requestBody.tip,
            paymentMethod =
                PaymentMethod.fromString(requestBody.paymentMethod) ?: PaymentMethod.CREDIT_CARD,
            cardParam =
                requestBody.cardParam?.let {
                    OrderRequest.CardParam(cardSequence = it.cardSequence)
                },
            convenienceParam =
                requestBody.convenienceParam?.let {
                    OrderRequest.ConvenienceParam(
                        convenience = it.convenience,
                        customerName = it.customerName,
                        customerKana = it.customerKana,
                        telNo = it.telNo,
                    )
                },
            googlePayParam =
                requestBody.googlePayParam?.let { OrderRequest.GooglePayParam(token = it.token) },
            applePayParam =
                requestBody.applePayParam?.let { OrderRequest.ApplePayParam(token = it.token) },
        )
    }
}
