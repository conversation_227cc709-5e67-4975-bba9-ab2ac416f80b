package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnore
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import io.quarkus.hibernate.orm.panache.kotlin.PanacheQuery
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "ranking_events")
class RankingEvent : BaseModel() {
    @Size(max = 255)
    @NotNull
    @Column(name = "event_identity", nullable = false)
    var eventIdentity: String? = null

    @Size(max = 255) @NotNull @Column(name = "name", nullable = false) var name: String? = null

    @NotNull
    @Column(name = "description", columnDefinition = "TEXT", nullable = false)
    var description: String? = null

    @Size(max = 255)
    @NotNull
    @Column(name = "image_url", nullable = false)
    var imageUrl: String? = null

    // Default value is "#B53599"
    @Size(max = 255) @Column(name = "base_color", nullable = false) var baseColor: String? = null

    @Size(max = 255)
    @Column(name = "add_infos", columnDefinition = "json")
    var addInfos: String? = null

    @Size(max = 255) @Column(name = "judge_x") var judgeX: String? = null

    @Size(max = 255) @Column(name = "judge_instagram") var judgeInstagram: String? = null

    @Size(max = 255) @Column(name = "share_hashtags") var shareHashTags: String? = null

    @Size(max = 255) @Column(name = "results") var results: String? = null

    @NotNull
    @Column(name = "apply_start_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var applyStartAt: Instant? = null

    @NotNull
    @Column(name = "apply_end_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var applyEndAt: Instant? = null

    @NotNull
    @Column(name = "start_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var startAt: Instant? = null

    @NotNull
    @Column(name = "end_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var endAt: Instant? = null

    @NotNull
    @Column(name = "calculated_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var calculatedAt: Instant? = null

    @NotNull
    @Column(name = "archived_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var archivedAt: Instant? = null

    @JsonIgnore
    @OneToMany(mappedBy = "rankingEvent", orphanRemoval = true)
    var rankingEventCreators: MutableSet<RankingEventCreator> = mutableSetOf()

    @JsonIgnore
    @OneToMany(mappedBy = "rankingEvent", orphanRemoval = true)
    var rankingFanBadges: MutableSet<RankingFanBadge> = mutableSetOf()

    @JsonIgnore
    @OneToMany(mappedBy = "rankingEvent", orphanRemoval = true)
    var rankingYellBoosts: MutableSet<RankingYellBoost> = mutableSetOf()

    companion object : PanacheCompanion<RankingEvent> {
        fun findActiveEventByEventIdentity(eventIdentity: String): RankingEvent? {
            return active()
                .filter("eventIdentity", mapOf("eventIdentity" to eventIdentity))
                .firstResult()
        }

        // active 状態
        fun active(): PanacheQuery<RankingEvent> {
            val now = Instant.now()
            return find("applyStartAt <= ?1 AND archivedAt > ?1", now)
        }

        // preparing 状態（申請開始前）
        fun preparing(): PanacheQuery<RankingEvent> {
            val now = Instant.now()
            return find("?1 < applyStartAt", now)
        }

        // accepting_entries 状態（申請受付中）
        fun acceptingEntries(): PanacheQuery<RankingEvent> {
            val now = Instant.now()
            return find("applyStartAt <= ?1 AND ?1 < applyEndAt", now)
        }

        // entries_closed 状態（申請受付終了、イベント開始前）
        fun entriesClosed(): PanacheQuery<RankingEvent> {
            val now = Instant.now()
            return find("applyEndAt <= ?1 AND ?1 < startAt", now)
        }

        // ongoing 状態（イベント開催中）
        fun ongoing(): PanacheQuery<RankingEvent> {
            val now = Instant.now()
            return find("startAt <= ?1 AND ?1 < endAt", now)
        }

        // calculating 状態（イベント終了後、結果集計中）
        fun calculating(): PanacheQuery<RankingEvent> {
            val now = Instant.now()
            return find("endAt <= ?1 AND ?1 < calculatedAt", now)
        }

        // announcing_results 状態（結果発表中）
        fun announcingResults(): PanacheQuery<RankingEvent> {
            val now = Instant.now()
            return find("calculatedAt <= ?1 AND ?1 < archivedAt", now)
        }

        // event_finished 状態（イベント終了後）
        fun eventFinished(): PanacheQuery<RankingEvent> {
            val now = Instant.now()
            return find("archivedAt <= ?1", now)
        }

        fun findActiveEventByCreator(user: User): RankingEvent? {
            return RankingEventCreator.find("creator = ?1", user)
                .list()
                .mapNotNull { it.rankingEvent }
                .firstOrNull { active().list().contains(it) }
        }
    }
}
