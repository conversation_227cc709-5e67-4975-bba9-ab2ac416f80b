package jp.co.torihada.fanme.endpoints.shop

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.Consumes
import jakarta.ws.rs.POST
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.DownloadUrlResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.controllers.FileController
import jp.co.torihada.fanme.modules.shop.usecases.file.GetDownloadUrls
import jp.co.torihada.fanme.modules.shop.usecases.file.GetPreSignedUrls
import jp.co.torihada.fanme.modules.shop.usecases.file.GetUploadUrls
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/shops/current/files")
class FileEndpoint {

    @Inject lateinit var requestContext: ContainerRequestContext
    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var fileHandler: FileController
    @Inject lateinit var userHandler: UserController
    @Inject lateinit var util: Util

    data class GetPreSignedUrlRequest(
        @JsonProperty("metadata_list") val metadataList: List<MetadataForGetPreSignedUrlRequest>,
        // クリエイターからショップを特定
        @JsonProperty("creator_account_identity") val creatorAccountIdentity: String,
    ) {
        data class MetadataForGetPreSignedUrlRequest(
            @JsonProperty("id") val id: String,
            @JsonProperty("key") val key: String,
        )
    }

    @POST
    @Path("/presigned-url")
    @Produces("application/json")
    @Consumes("application/json")
    fun getPreSignedUrl(request: GetPreSignedUrlRequest): Response {
        val creator =
            userHandler.getUserByAccountIdentity(request.creatorAccountIdentity.toString())
                ?: throw ResourceNotFoundException("User")
        return try {
            val result =
                fileHandler.getPreSignedUrl(
                    creator.uid!!,
                    request.metadataList.map {
                        GetPreSignedUrls.PreSignedFileMetadata(it.id, it.key)
                    },
                )
            val entity = ResponseEntity(result, "presigned_urls")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class GetUploadUrlRequest(
        @JsonProperty("metadataList") val metadataList: List<MetadataForGetUploadUrlRequest>
    ) {
        data class MetadataForGetUploadUrlRequest(
            @JsonProperty("id") val id: String,
            @JsonProperty("name") val name: String?,
        )
    }

    @POST
    @Path("/upload-url")
    @RolesAllowed("LoginUser")
    @Consumes("application/json")
    @Produces("application/json")
    fun getUploadUrl(request: GetUploadUrlRequest): Response {
        val isPublic =
            requestContext.uriInfo.queryParameters.getFirst("is_public")?.toBoolean() ?: false
        return try {
            val creatorUid =
                util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result =
                fileHandler.getUploadUrls(
                    creatorUid,
                    request.metadataList.map { GetUploadUrls.UploadFileMetadata(it.id, it.name) },
                    isPublic,
                )
            val entity = ResponseEntity(result, "upload_urls")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class GetDownloadUrlRequest(
        @JsonAlias("metadataList")
        @JsonProperty("metadata_list")
        val metadataList: List<MetadataForGetDownloadUrlRequest>,
        // Itemからショップを特定
        @JsonAlias("itemId") @JsonProperty("item_id") val itemId: Long,
    ) {
        data class MetadataForGetDownloadUrlRequest(
            @JsonProperty("key") val key: String,
            @JsonProperty("name") val name: String?,
        )
    }

    @POST
    @Path("/download-url")
    @RolesAllowed("LoginUser")
    @Consumes("application/json")
    @Produces("application/json")
    @APIResponse(responseCode = "200")
    @APIResponseSchema(DownloadUrlResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getDownloadUrl(request: GetDownloadUrlRequest): Response {
        return try {
            // TODO getUploadUrlと同様にidでファイルを識別する
            val result =
                fileHandler.getDownloadUrls(
                    request.itemId,
                    request.metadataList.map {
                        GetDownloadUrls.DownloadFileMetadata(it.key, it.name)
                    },
                )
            val entity = ResponseEntity(result, "download_urls")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
