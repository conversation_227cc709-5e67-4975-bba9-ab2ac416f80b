package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.usecases.GetAgencies
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class AgencyController {

    @Inject private lateinit var getAgency: GetAgencies

    fun getAgencies(odata: OData?): List<Agency> {
        return getAgency.execute(GetAgencies.Input(odata)).getOrThrow()
    }
}
