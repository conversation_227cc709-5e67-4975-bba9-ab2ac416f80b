package jp.co.torihada.backend.externals.entity.payment

import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.runtime.annotations.RegisterForReflection

@RegisterForReflection
class ExtGetSellerAppItem {

    data class Response(
        @JsonProperty("id") val id: Long,
        @JsonProperty("created_at") val createdAt: String,
        @JsonProperty("updated_at") val updatedAt: String,
        @JsonProperty("tenant") val tenant: String,
        @JsonProperty("app_item_id") val appItemId: Long,
        @JsonProperty("seller_app_id") val sellerAppId: Long,
        @JsonProperty("name") val name: String,
        @JsonProperty("description") val description: String?,
        @JsonProperty("icon_url") val iconUrl: String,
        @JsonProperty("locked_icon_url") val lockedIconUrl: String?,
        @JsonProperty("image_url1") val imageUrl1: String,
        @JsonProperty("image_url2") val imageUrl2: String,
        @JsonProperty("image_url3") val imageUrl3: String?,
        @JsonProperty("image_url4") val imageUrl4: String?,
        @JsonProperty("image_url5") val imageUrl5: String?,
        @JsonProperty("sort_order") val sortOrder: Int,
        @JsonProperty("available") val available: Boolean,
        @JsonProperty("purchase_type") val purchaseType: Int,
    )
}
