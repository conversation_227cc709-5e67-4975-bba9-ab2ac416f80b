package jp.co.torihada.fanme.endpoints.shop

import io.quarkus.security.identity.SecurityIdentity
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.payment.controllers.SalesHistoryController
import jp.co.torihada.fanme.odata.OData

@Path("/shops/current/sales-history")
class SalesHistoryEndpoint {

    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var requestContext: ContainerRequestContext
    @Inject lateinit var handler: SalesHistoryController
    @Inject lateinit var util: Util

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    fun getSalesHistories(): Response {
        val odata = requestContext.getProperty("odata") as OData?
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.getSalesHistories(userUid, odata)
            val entity = ResponseEntity(result, "sales_histories")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
