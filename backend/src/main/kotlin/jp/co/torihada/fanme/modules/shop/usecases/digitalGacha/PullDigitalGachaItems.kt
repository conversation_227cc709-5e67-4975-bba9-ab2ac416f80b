package jp.co.torihada.fanme.modules.shop.usecases.digitalGacha

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.DigitalGachaNotPurchasedException
import jp.co.torihada.fanme.exception.DigitalGachaPullCountExceededException
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.models.*
import jp.co.torihada.fanme.modules.shop.services.aws.S3

@ApplicationScoped
class PullDigitalGachaItems {
    @Inject lateinit var s3: S3

    data class Input(val itemId: Long, val purchaserUid: String, val pullCount: Int?)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class FileForPullDigitalGachaItems(
        val id: Long,
        val name: String,
        val objectUri: String?,
        val thumbnailUri: String?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
        val awardType: Int,
        val isSecret: Boolean?,
    )

    fun execute(input: Input): Result<List<FileForPullDigitalGachaItems>, FanmeException> {
        val (unpulledPurchasedItems, maxPullCount) =
            validatePurchaseAndCalculateMaxPullCount(
                purchaserUid = input.purchaserUid,
                itemId = input.itemId,
            )
        val pullCount = determinePullCount(input.pullCount, maxPullCount)

        val gachaItem =
            GachaItem.findByItemId(input.itemId) ?: throw ResourceNotFoundException("GachaItem")

        val receivedFiles =
            GachaReceivedFile.findByPurchaserUidAndItemId(input.purchaserUid, input.itemId)

        val itemFiles =
            if (gachaItem.isDuplicated) {
                pullDuplicatedItems(gachaItem, pullCount)
            } else {
                pullUniqueItems(gachaItem, receivedFiles, pullCount)
            }

        // itemに対応したpurchasedItem.idを紐づける(複数回購入後にまとめてガチャを回した時用)
        val itemQueue = ArrayDeque(itemFiles)
        // 既に割り当てられている数を計算する
        val alreadyPulledMap =
            GachaReceivedFile.findByPurchaserUidAndItemId(input.purchaserUid, input.itemId)
                .groupingBy { it?.purchasedItem?.id }
                .eachCount()

        val gachaReceivedFiles = mutableListOf<GachaReceivedFile>()

        for (purchasedItem in unpulledPurchasedItems) {
            val purchasedItemId = purchasedItem.id
            val alreadyPulled = alreadyPulledMap[purchasedItemId] ?: 0
            val remaining = purchasedItem.quantity - alreadyPulled

            val purchasedItemModel =
                PurchasedItem.findById(purchasedItemId!!)
                    ?: throw ResourceNotFoundException("PurchasedItem")

            repeat(remaining) {
                val itemFile = itemQueue.removeFirstOrNull() ?: return@repeat
                val itemFileModel =
                    ItemFile.findById(itemFile.id) ?: throw ResourceNotFoundException("itemFile")

                gachaReceivedFiles +=
                    GachaReceivedFile().apply {
                        this.purchasedItem = purchasedItemModel
                        this.itemFile = itemFileModel
                    }
            }

            if (itemQueue.isEmpty()) break
        }
        GachaReceivedFile.createBulk(gachaReceivedFiles)

        val signedItemFiles =
            itemFiles.map {
                FileForPullDigitalGachaItems(
                    id = it.id,
                    name = it.name,
                    objectUri = it.objectUri?.let { uri -> s3.getObjectUri(uri) },
                    thumbnailUri = it.thumbnailUri?.let { uri -> s3.getObjectUri(uri) },
                    fileType = it.fileType,
                    size = it.size,
                    duration = it.duration,
                    awardType = it.awardType,
                    isSecret = it.isSecret,
                )
            }
        return Ok(signedItemFiles)
    }

    private fun pullDuplicatedItems(
        gachaItem: GachaItem,
        pullCount: Int,
    ): List<FileForPullDigitalGachaItems> {
        val gachaProbabilities = GachaProbability.findByGachaItemId(gachaItem.id!!)
        val sortedProbabilities =
            gachaProbabilities
                .map { it.awardType to it.probability }
                .sortedByDescending { it.second }

        val cumulativeMap =
            sortedProbabilities
                .runningFold(0 to 0) { (accProb, _), (awardType, probability) ->
                    accProb + probability to awardType.value
                }
                .drop(1)
                .associate { (cumProb, awardType) -> AwardType.fromValue(awardType) to cumProb }

        val itemFiles = ItemFile.findByItemId(gachaItem.item.id!!)
        val gachaItemFilesMap = itemFiles.associateWith { GachaItemFile.findByItemFileId(it.id!!) }

        return List(pullCount) {
                val randomValue = (1..100).random()
                val selectedAwardType =
                    cumulativeMap.entries
                        .sortedBy { it.value }
                        .firstOrNull { (_, cumulative) -> randomValue <= cumulative }
                        ?.key

                val selected =
                    selectedAwardType?.let { awardType ->
                        gachaItemFilesMap
                            .filter { (_, gachaFile) -> gachaFile?.awardType == awardType }
                            .keys
                            .randomOrNull()
                            ?.let { file ->
                                FileForPullDigitalGachaItems(
                                    id = file.id!!,
                                    name = file.name,
                                    objectUri = file.objectUri,
                                    thumbnailUri = file.thumbnailUri,
                                    fileType = file.fileType,
                                    size = file.size,
                                    duration = file.duration,
                                    awardType = awardType.value,
                                    isSecret = gachaItemFilesMap[file]?.isSecret,
                                )
                            }
                    }
                selected
            }
            .filterNotNull()
    }

    private fun pullUniqueItems(
        gachaItem: GachaItem,
        receivedFiles: List<GachaReceivedFile?>,
        pullCount: Int,
    ): List<FileForPullDigitalGachaItems> {
        val receivedFileIds = receivedFiles.mapNotNull { it?.itemFile?.id }
        val availableFiles =
            ItemFile.findByItemId(gachaItem.item.id!!)
                .filter { it.id !in receivedFileIds }
                .toMutableList()

        if (pullCount > availableFiles.size) {
            throw DigitalGachaPullCountExceededException()
        }

        val result = mutableListOf<FileForPullDigitalGachaItems>()
        repeat(pullCount.coerceAtMost(availableFiles.size)) {
            val selectedItem = availableFiles.random()
            availableFiles.remove(selectedItem)
            result.add(
                FileForPullDigitalGachaItems(
                    id = selectedItem.id!!,
                    name = selectedItem.name,
                    objectUri = selectedItem.objectUri,
                    thumbnailUri = selectedItem.thumbnailUri,
                    fileType = selectedItem.fileType,
                    size = selectedItem.size,
                    duration = selectedItem.duration,
                    awardType = selectedItem.gachaItemFile?.awardType?.value ?: AwardType.C.value,
                    isSecret = selectedItem.gachaItemFile?.isSecret,
                )
            )
        }
        return result
    }

    private fun validatePurchaseAndCalculateMaxPullCount(
        purchaserUid: String,
        itemId: Long,
    ): Pair<List<PurchasedItem>, Int> {
        val unpulledPurchasedItems =
            PurchasedItem.findUnpulledGachaItemsByPurchaserUidAndItemId(
                    purchaserUid = purchaserUid,
                    itemId = itemId,
                )
                .filterNotNull()

        if (unpulledPurchasedItems.isEmpty()) {
            throw DigitalGachaNotPurchasedException()
        }

        val maxPullCount = unpulledPurchasedItems.sumOf { it.quantity }
        if (maxPullCount <= 0) {
            throw DigitalGachaPullCountExceededException()
        }

        return unpulledPurchasedItems to maxPullCount
    }

    private fun determinePullCount(inputCount: Int?, maxCount: Int): Int {
        return inputCount
            ?.takeIf { it > 0 }
            ?.let {
                if (it > maxCount) {
                    throw DigitalGachaPullCountExceededException()
                }
                it
            } ?: maxCount
    }
}
