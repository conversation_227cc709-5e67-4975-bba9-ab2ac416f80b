package jp.co.torihada.fanme.modules.fanme.services.fanmeCustomer

import io.quarkus.arc.DefaultBean
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.externals.client.salesforce.entities.fanmeCustomer.FanmeCustomerRecord
import jp.co.torihada.fanme.modules.fanme.externals.entity.salesforce.CreateRecordRequestBody
import jp.co.torihada.fanme.modules.fanme.services.SalesforceService

@DefaultBean
@ApplicationScoped
class FanmeCustomerService : SalesforceService(), IFanmeCustomerService {
    override fun get(creatorUid: String): FanmeCustomerEntity? {
        val response =
            salesforceClient.executeQuery("Bearer ${getToken()}", createGetQuery(creatorUid))

        try {
            val record = parseFirstRecord<FanmeCustomerRecord>(response)
            return record?.let { FanmeCustomerMapper.fromRecord(it) }
        } catch (_: Exception) {
            // 住所未登録の場合は null を返す
            return null
        }
    }

    override fun create(entity: FanmeCustomerEntity) {
        val body =
            CreateRecordRequestBody(
                allOrNone = true,
                records = listOf(FanmeCustomerMapper.toRecord(entity)),
            )
        salesforceClient.createRecord("Bearer ${getToken()}", body)
    }

    override fun update(creatorUid: String, entity: FanmeCustomerEntity) {
        val record = FanmeCustomerMapper.toRecord(entity)

        salesforceClient.updateRecord(
            "Bearer ${getToken()}",
            "FanmeCustomer__c",
            "FanmeUserId__c",
            creatorUid,
            record,
        )
    }

    private fun createGetQuery(creatorUid: String): String {
        return """
            SELECT
                Id,
                FanmeUserId__c,
                Name__c,
                NameKana__c,
                PostalCode__c,
                Prefecture__c,
                City__c,
                Street__c,
                PhoneNumber__c,
                Building__c
            FROM FanmeCustomer__c
            WHERE FanmeUserId__c IN ('$creatorUid')
        """
            .trimIndent()
            .replace("    ", " ")
            .replace("\n", " ")
    }
}
