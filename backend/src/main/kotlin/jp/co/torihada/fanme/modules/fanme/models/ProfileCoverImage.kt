package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonBackReference
import com.fasterxml.jackson.annotation.JsonManagedReference
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "profile_cover_images")
class ProfileCoverImage : BaseModel() {
    @NotNull
    @ManyToOne(optional = false)
    @JsonBackReference
    @JoinColumn(name = "profile_cover_id", nullable = false)
    var profileCover: ProfileCover? = null

    @Size(max = 255)
    @NotNull
    @Column(name = "resource", nullable = false)
    var resource: String? = null

    @Size(max = 255)
    @NotNull
    @Column(name = "resource_type", nullable = false)
    var resourceType: String? = null

    @JsonManagedReference
    @OneToOne(mappedBy = "profileCoverImage", orphanRemoval = true)
    var displayableCoverImage: DisplayableCoverImage? = null

    companion object : PanacheCompanion<ProfileCoverImage> {
        fun findByProfileCoverAndResourceType(
            profileCover: ProfileCover,
            resourceType: String,
        ): ProfileCoverImage? {
            return find("profileCover = ?1 and resourceType = ?2", profileCover, resourceType)
                .firstResult()
        }
    }
}
