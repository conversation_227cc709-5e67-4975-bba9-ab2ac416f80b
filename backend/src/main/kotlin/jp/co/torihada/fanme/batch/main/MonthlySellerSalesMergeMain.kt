package jp.co.torihada.fanme.batch.main

import io.quarkus.runtime.QuarkusApplication
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.batch.usecases.MonthlySellerSalesMerge
import org.jboss.logging.Logger

class MonthlySellerSalesMergeMain : QuarkusApplication {
    private val logger: Logger = Logger.getLogger(MonthlySellerSalesMergeMain::class.java)

    @Inject private lateinit var monthlySellerSalesMerge: MonthlySellerSalesMerge

    @Transactional
    override fun run(vararg args: String?): Int {
        logger.info("MonthlySellerSalesMergeMain start")

        val yearMonth =
            if (args.size > 1 && args[1] != null) {
                args[1]
            } else {
                val previousDate: LocalDateTime =
                    ZonedDateTime.now(ZoneId.of("Asia/Tokyo")).minusMonths(1).toLocalDateTime()

                DateTimeFormatter.ofPattern("yyyyMM").format(previousDate)
            }

        try {
            monthlySellerSalesMerge.execute(yearMonth!!)
            logger.info("MonthlySellerSalesMergeMain success yearMonth: $yearMonth")
            return 0
        } catch (e: Exception) {
            logger.error("MonthlySellerSalesMergeMain error yearMonth: $yearMonth", e)
            return 1
        }
    }
}
