package jp.co.torihada.fanme.batch.main

import io.quarkus.runtime.QuarkusApplication
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.batch.usecases.SellerGmoTransferStatusUpdate
import org.jboss.logging.Logger

class SellerGmoTransferStatusUpdateMain : QuarkusApplication {
    private val logger: Logger = Logger.getLogger(SellerGmoTransferStatusUpdateMain::class.java)

    @Inject private lateinit var sellerGmoTransferStatusUpdateBatch: SellerGmoTransferStatusUpdate

    @Transactional
    override fun run(vararg args: String?): Int {
        logger.info("SellerGmoTransferStatusUpdateMain start")
        try {
            sellerGmoTransferStatusUpdateBatch.execute()
            logger.info("SellerGmoTransferStatusUpdateMain success")
            return 0
        } catch (e: Exception) {
            logger.error("SellerGmoTransferStatusUpdateMain error", e)
            return 1
        }
    }
}
