package jp.co.torihada.fanme.endpoints.shop

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalGacha.create.CreateDigitalGachaItemRequest
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalGacha.create.CreateDigitalGachaItemRequestConverter
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalGacha.update.UpdateDigitalGachaItemRequest
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalGacha.update.UpdateDigitalGachaItemRequestConverter
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.shop.controllers.DigitalGachaController

@Path("/shops/current/digital-gacha")
class CurrentUserDigitalGachaEndpoint {

    @Inject private lateinit var securityIdentity: SecurityIdentity
    @Inject private lateinit var requestContext: ContainerRequestContext
    @Inject private lateinit var handler: DigitalGachaController
    @Inject private lateinit var createDigitalGachaConverter: CreateDigitalGachaItemRequestConverter
    @Inject private lateinit var updateDigitalGachaConverter: UpdateDigitalGachaItemRequestConverter
    @Inject private lateinit var util: Util

    @POST
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun createDigitalGachaItem(requestBody: CreateDigitalGachaItemRequest): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val request = createDigitalGachaConverter.requestToCreateItem(userUid, requestBody)
            val result = handler.createItem(request)
            val entity = ResponseEntity(result, "item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @PUT
    @RolesAllowed("LoginUser")
    @Path("/{item_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun updateDigitalGachaItem(
        @PathParam("item_id") id: Long,
        requestBody: UpdateDigitalGachaItemRequest,
    ): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val request = updateDigitalGachaConverter.requestToUpdateItem(userUid, id, requestBody)
            val result = handler.updateItem(request)
            val entity = ResponseEntity(result, "item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
