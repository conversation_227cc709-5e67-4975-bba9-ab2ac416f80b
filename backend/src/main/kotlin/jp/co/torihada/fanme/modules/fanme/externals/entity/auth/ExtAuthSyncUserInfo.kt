package jp.co.torihada.backend.externals.entity.auth

import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.runtime.annotations.RegisterForReflection
import java.time.Instant

@RegisterForReflection
class ExtAuthSyncUserInfo {
    data class Request(
        @JsonProperty("name") val uid: String?,
        @JsonProperty("gender") val gender: String?,
        @JsonProperty("birthday") val birthday: Instant?,
        @JsonProperty("birthday_confirmed") val birthdayConfirmed: Boolean?,
    )
}
