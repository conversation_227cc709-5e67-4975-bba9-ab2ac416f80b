package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnore
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "ranking_event_creators")
class RankingEventCreator : BaseModel() {

    @ManyToOne
    @JoinColumn(name = "ranking_event_id", nullable = false)
    var rankingEvent: RankingEvent? = null

    @ManyToOne @JoinColumn(name = "creator_id", nullable = false) var creator: User? = null

    @NotNull
    @Column(name = "comment", columnDefinition = "TEXT", nullable = false)
    var comment: String? = null

    // Default value is 0
    @Column(name = "status", nullable = false) var status: Int? = null

    @Column(name = "rank") var rank: Int? = null

    @Column(name = "yell_count") var yellCount: Int? = null

    @Column(name = "aggregation_at")
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var aggregationAt: Instant? = null

    @Column(name = "previous_rank") var previousRank: Int? = null

    @Column(name = "previous_yell_count") var previousYellCount: Int? = null

    @Column(name = "previous_aggregation_at")
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var previousAggregationAt: Instant? = null

    @JsonIgnore
    @OneToMany(mappedBy = "rankingEventCreator", orphanRemoval = true)
    var rankingEventUsers: MutableSet<RankingEventUser> = mutableSetOf()

    companion object : PanacheCompanion<RankingEventCreator> {
        fun findByEventAndCreator(rankingEvent: RankingEvent, user: User): RankingEventCreator? {
            return find("rankingEvent = ?1 and creator = ?2", rankingEvent, user).firstResult()
        }
    }

    enum class Status(val value: Int) {
        APPLICATION(0),
        PARTICIPATING(1),
        REJECTED(2),
    }
}
