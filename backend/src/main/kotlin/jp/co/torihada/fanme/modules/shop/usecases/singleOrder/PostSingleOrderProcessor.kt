package jp.co.torihada.fanme.modules.shop.usecases.singleOrder

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.shop.Util.*
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.OrderItem
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import jp.co.torihada.fanme.modules.shop.models.Shop
import org.jboss.logging.Logger

@ApplicationScoped
class PostSingleOrderProcessor {
    @Inject lateinit var logger: Logger

    data class Input(
        val userId: String,
        val shop: Shop,
        val transaction: Transaction?,
        val checkoutId: Long,
        val amounts: OrderAmounts,
        val purchasedItemStatus: PurchasedItemStatus,
        val paymentMethod: PaymentMethod,
        val itemId: Long,
        val quantity: Int,
    )

    data class Output(val order: Order, val purchasedItems: List<PurchasedItem>? = null)

    fun execute(params: Input): Result<Output, FanmeException> {
        // オーダーの更新
        val order =
            Order.updateOrder(
                transactionId = params.transaction?.id,
                checkoutId = params.checkoutId,
            )
        if (order == null) {
            return Err(ResourceNotFoundException("Order"))
        }

        if (params.paymentMethod == PaymentMethod.CREDIT_CARD) {
            OrderItem.create(
                orderId = order.id!!,
                itemId = params.itemId,
                quantity = params.quantity,
            )
        } else {
            PurchasedItem.create(
                orderId = order.id!!,
                purchaserUid = params.userId,
                itemId = params.itemId,
                price = params.amounts.unitPrices.find { params.itemId == it.item.id }!!.price,
                quantity = params.quantity,
                status = params.purchasedItemStatus.value,
                itemFileId = null, // こちらnullで以下確認
            )

            val purchasedItems = PurchasedItem.findByOrderId(order.id!!)
            return Ok(Output(order, purchasedItems))
        }

        return Ok(Output(order))
    }
}
