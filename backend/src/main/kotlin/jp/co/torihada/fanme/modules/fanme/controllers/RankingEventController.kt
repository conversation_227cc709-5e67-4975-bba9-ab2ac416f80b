package jp.co.torihada.fanme.modules.fanme.controllers

import Base<PERSON>ontroller
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import java.math.BigDecimal
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.*

@ApplicationScoped
class RankingEventController : BaseController() {

    data class RankingEventWithBoost(val rankingEvent: RankingEvent?, val boost: RankingYellBoost?)

    fun activeEvent(userUuid: String): RankingEventWithBoost {
        val user = User.findByUuid(userUuid) ?: throw ResourceNotFoundException("User")
        val rankingEvent = RankingEvent.findActiveEventByCreator(user)
        val boost = rankingEvent?.let { RankingYellBoost.findActiveBoost(it) }

        return RankingEventWithBoost(rankingEvent, boost)
    }

    @Transactional
    fun insertPurchaseLog(
        userUuid: String,
        targetCreatorUuid: String,
        transactionId: Long,
        amount: Int,
        tipAmount: Int? = null,
    ) {
        val user = User.findByUuid(userUuid) ?: throw ResourceNotFoundException("User")
        val targetCreator =
            User.findByUuid(targetCreatorUuid) ?: throw ResourceNotFoundException("Creator")

        // ブースト中かの確認
        val rankingEvent = RankingEvent.findActiveEventByCreator(targetCreator)
        val boostRatio =
            rankingEvent?.let { RankingYellBoost.findActiveBoost(it) }?.boostRatio ?: BigDecimal.ONE

        // エールログの挿入
        UserYellLog.insertPurchaseLog(
            user,
            targetCreator,
            transactionId,
            amount = (BigDecimal(amount) * boostRatio).toInt(),
            tipAmount =
                if (tipAmount != null) (BigDecimal(tipAmount) * boostRatio).toInt() else null,
        )
        UserGeneralEventYellLog.insertPurchaseLog(
            user,
            targetCreator,
            transactionId,
            amount,
            tipAmount,
        )
    }
}
