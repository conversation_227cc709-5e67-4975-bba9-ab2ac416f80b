package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "creator_states")
class UserState : BaseModel() {
    @NotNull @ManyToOne @JoinColumn(name = "creator_id") var user: User? = null

    @Size(max = 255) @NotNull @Column(name = "`key`", nullable = false) var key: String? = null

    @Size(max = 255) @Column(name = "value") var value: String? = null

    @Column(name = "data", columnDefinition = "TEXT") var data: String? = null

    companion object : PanacheCompanion<UserState> {
        fun findByCreatorAndKey(user: User, key: String): UserState? {
            return find("user = ?1 and key = ?2", user, key).firstResult()
        }
    }
}
