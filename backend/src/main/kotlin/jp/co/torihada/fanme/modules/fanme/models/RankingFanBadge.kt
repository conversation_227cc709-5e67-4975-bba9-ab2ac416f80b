package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import jakarta.persistence.*

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "ranking_fan_badges")
class RankingFanBadge : BaseModel() {

    @ManyToOne
    @JoinColumn(name = "ranking_event_id", nullable = false)
    var rankingEvent: RankingEvent? = null

    // Default value is 0
    @Column(name = "rank", nullable = false) var rank: Int? = null

    @Column(name = "badge_url", columnDefinition = "TEXT", nullable = false)
    var badgeUrl: String? = null

    @Column(name = "badge_top_url", columnDefinition = "TEXT") var badgeTopUrl: String? = null

    @Column(name = "image_url", columnDefinition = "TEXT", nullable = false)
    var imageUrl: String? = null

    @OneToMany(mappedBy = "rankingFanBadge", orphanRemoval = true)
    var userRankingFanBadges: MutableSet<UserRankingFanBadge> = mutableSetOf()
}
