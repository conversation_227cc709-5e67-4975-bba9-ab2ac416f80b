name: fanme-shop-backend
services:
  app:
    image: openjdk:17
    environment:
      QUARKUS_ANALYTICS_DISABLED: true
      QUARKUS_PROFILE: docker
      KTOR_ENV: docker
      GRADLE_OPTS: "-Dorg.gradle.daemon=true -Dorg.gradle.parallel=true"
    container_name: fanme-backend-app
    command: ./gradlew quarkusDev -Dquarkus.http.host=0.0.0.0 -Dquarkus.profile=local
    volumes:
      - ..:/app
      - ./run/gradle-app:/app/.gradle
      - ./run/gradle-app-cache:/root/.gradle
    working_dir: /app
    ports:
      - "27000:8080"

  db:
    restart: on-failure
    image: mysql/mysql-server:8.0
    container_name: fanme-shop-db
    command: --default-authentication-plugin=mysql_native_password
    environment:
      MYSQL_DATABASE: shop
      MYSQL_ROOT_PASSWORD: pass
      MYSQL_ROOT_HOST: '%'
    volumes:
      - ./run/mysql:/var/lib/mysql
    ports:
      - "27001:3306"
